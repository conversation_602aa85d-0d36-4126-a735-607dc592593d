// Debug Console <PERSON> for Three.js Integration Demo
// Run this in the browser console to diagnose issues

console.log('🔍 Starting Three.js Integration Debug...');

// 1. Check if Three.js is loaded
console.log('\n1. Checking Three.js availability:');
if (typeof THREE !== 'undefined') {
    console.log('✅ THREE is available');
    console.log('   Version:', THREE.REVISION);
    console.log('   Available classes:', Object.keys(THREE).slice(0, 10).join(', '), '...');
} else {
    console.error('❌ THREE is not available - CDN loading failed');
}

// 2. Check OrbitControls
console.log('\n2. Checking OrbitControls:');
if (typeof THREE !== 'undefined' && typeof THREE.OrbitControls !== 'undefined') {
    console.log('✅ OrbitControls is available');
} else {
    console.error('❌ OrbitControls is not available');
}

// 3. Check math classes
console.log('\n3. Checking math classes:');
const mathClasses = ['Vector2', 'Vector3', 'Matrix4', 'Quaternion'];
mathClasses.forEach(className => {
    if (typeof window[className] !== 'undefined') {
        console.log(`✅ ${className} is available`);
        try {
            const instance = new window[className]();
            console.log(`   ${className} instance created successfully:`, instance);
        } catch (error) {
            console.error(`❌ Failed to create ${className} instance:`, error);
        }
    } else {
        console.error(`❌ ${className} is not available`);
    }
});

// 4. Check VisioWeb Essential classes
console.log('\n4. Checking VisioWeb Essential classes:');
const vwClasses = ['VisioWebEssential', 'ContentManager', 'NavigationManager', 'RouteManager', 'VenueManager'];
vwClasses.forEach(className => {
    if (typeof window[className] !== 'undefined') {
        console.log(`✅ ${className} is available`);
    } else {
        console.warn(`⚠️ ${className} is not available (optional)`);
    }
});

// 5. Check Three.js scene variables
console.log('\n5. Checking Three.js scene variables:');
const sceneVars = ['scene', 'camera', 'renderer', 'controls'];
sceneVars.forEach(varName => {
    if (typeof window[varName] !== 'undefined' && window[varName] !== null) {
        console.log(`✅ ${varName} is initialized`);
    } else {
        console.error(`❌ ${varName} is not initialized`);
    }
});

// 6. Check DOM elements
console.log('\n6. Checking DOM elements:');
const elements = [
    'threejs-canvas',
    'loading', 
    'info-panel',
    'status',
    'scene-info',
    'object-count',
    'fps-counter',
    'math-output'
];
elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ Element #${id} found`);
    } else {
        console.error(`❌ Element #${id} not found`);
    }
});

// 7. Test math operations
console.log('\n7. Testing math operations:');
try {
    if (typeof window.Vector3 !== 'undefined') {
        const v1 = new window.Vector3(1, 2, 3);
        const v2 = new window.Vector3(4, 5, 6);
        const sum = v1.clone().add(v2);
        console.log('✅ Vector3 math operations work:', {
            v1: `(${v1.x}, ${v1.y}, ${v1.z})`,
            v2: `(${v2.x}, ${v2.y}, ${v2.z})`,
            sum: `(${sum.x}, ${sum.y}, ${sum.z})`
        });
    }
} catch (error) {
    console.error('❌ Vector3 math operations failed:', error);
}

// 8. Test Three.js scene creation
console.log('\n8. Testing Three.js scene creation:');
try {
    if (typeof THREE !== 'undefined') {
        const testScene = new THREE.Scene();
        const testGeometry = new THREE.BoxGeometry(1, 1, 1);
        const testMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const testMesh = new THREE.Mesh(testGeometry, testMaterial);
        testScene.add(testMesh);
        console.log('✅ Three.js scene creation works');
        console.log('   Test scene children:', testScene.children.length);
    }
} catch (error) {
    console.error('❌ Three.js scene creation failed:', error);
}

// 9. Check network requests
console.log('\n9. Checking network requests:');
const testUrls = [
    './src/math/Vector2.js',
    './src/math/Vector3.js',
    './src/math/Matrix4.js',
    './src/math/Quaternion.js'
];

testUrls.forEach(async (url) => {
    try {
        const response = await fetch(url);
        if (response.ok) {
            console.log(`✅ ${url} - Status: ${response.status}`);
        } else {
            console.error(`❌ ${url} - Status: ${response.status}`);
        }
    } catch (error) {
        console.error(`❌ ${url} - Network error:`, error);
    }
});

// 10. Check console errors
console.log('\n10. Console Error Summary:');
console.log('Check the Console tab for any red error messages.');
console.log('Common issues to look for:');
console.log('- Module loading errors (import/export)');
console.log('- CORS errors');
console.log('- 404 Not Found errors');
console.log('- TypeError: Cannot read property errors');
console.log('- ReferenceError: variable is not defined');

console.log('\n🔍 Debug complete! Check the results above.');
console.log('If you see any ❌ errors, those need to be fixed.');
console.log('⚠️ warnings are optional features that may not be available.');

// Helper function to test demo functions
window.debugTestDemo = function() {
    console.log('\n🧪 Testing demo functions:');
    
    const demoFunctions = [
        'testVector3',
        'testVector2', 
        'testMatrix4',
        'testQuaternion',
        'createBasicScene',
        'createMathDemo',
        'clearScene'
    ];
    
    demoFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} function is available`);
            try {
                // Don't actually call the functions, just check they exist
                console.log(`   ${funcName} is callable`);
            } catch (error) {
                console.error(`❌ ${funcName} failed:`, error);
            }
        } else {
            console.error(`❌ ${funcName} function not found`);
        }
    });
};

console.log('\n💡 Run debugTestDemo() to test demo functions availability.');
