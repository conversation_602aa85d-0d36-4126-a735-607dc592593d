<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Integration Test</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #1a1a2e; color: white; }
        #canvas { border: 1px solid #333; }
        .controls { margin: 20px 0; }
        button { margin: 5px; padding: 10px; background: #4caf50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        #output { margin: 20px 0; padding: 10px; background: #333; border-radius: 5px; }
        .error { color: #ff6b6b; }
        .success { color: #4caf50; }
    </style>
</head>
<body>
    <h1>Three.js Integration Test</h1>
    <div id="output">Loading...</div>
    
    <div class="controls">
        <button onclick="testVector3()">Test Vector3</button>
        <button onclick="testBasicScene()">Test Basic Scene</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>
    
    <canvas id="canvas" width="800" height="600"></canvas>

    <!-- Load Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>

    <script type="module">
        // Check if Three.js loaded
        if (typeof THREE === 'undefined') {
            document.getElementById('output').innerHTML = '<span class="error">❌ Three.js failed to load!</span>';
            throw new Error('Three.js not loaded');
        }

        // Load our math classes
        let Vector3, scene, camera, renderer, controls;
        
        try {
            const { Vector3: V3 } = await import('./src/math/Vector3.js');
            Vector3 = V3;
            window.Vector3 = Vector3;
            
            // Initialize Three.js
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, 800/600, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('canvas') });
            renderer.setSize(800, 600);
            renderer.setClearColor(0x1a1a2e);
            
            // Add basic lighting
            const light = new THREE.DirectionalLight(0xffffff, 1);
            light.position.set(5, 5, 5);
            scene.add(light);
            
            // Set camera position
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            // Add controls if available
            if (typeof THREE.OrbitControls !== 'undefined') {
                controls = new THREE.OrbitControls(camera, renderer.domElement);
            }
            
            // Start render loop
            function animate() {
                requestAnimationFrame(animate);
                if (controls) controls.update();
                renderer.render(scene, camera);
            }
            animate();
            
            document.getElementById('output').innerHTML = '<span class="success">✅ Three.js and Vector3 loaded successfully!</span>';
            
        } catch (error) {
            document.getElementById('output').innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
            console.error('Error:', error);
        }

        // Test functions
        window.testVector3 = function() {
            try {
                const v1 = new Vector3(1, 2, 3);
                const v2 = new Vector3(4, 5, 6);
                const sum = v1.clone().add(v2);
                
                document.getElementById('output').innerHTML = `
                    <span class="success">✅ Vector3 Test Results:</span><br>
                    v1: (${v1.x}, ${v1.y}, ${v1.z})<br>
                    v2: (${v2.x}, ${v2.y}, ${v2.z})<br>
                    v1 + v2: (${sum.x}, ${sum.y}, ${sum.z})<br>
                    Length of v1: ${v1.length().toFixed(2)}
                `;
            } catch (error) {
                document.getElementById('output').innerHTML = `<span class="error">❌ Vector3 test failed: ${error.message}</span>`;
            }
        };

        window.testBasicScene = function() {
            try {
                // Clear scene
                while(scene.children.length > 0) {
                    scene.remove(scene.children[0]);
                }
                
                // Re-add light
                const light = new THREE.DirectionalLight(0xffffff, 1);
                light.position.set(5, 5, 5);
                scene.add(light);
                
                // Create a cube using our Vector3 for positioning
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                const material = new THREE.MeshPhongMaterial({ color: 0x4caf50 });
                const cube = new THREE.Mesh(geometry, material);
                
                const position = new Vector3(0, 0, 0);
                cube.position.copy(position);
                scene.add(cube);
                
                document.getElementById('output').innerHTML = '<span class="success">✅ Basic scene created with rotating cube!</span>';
                
                // Animate the cube
                function animateCube() {
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    requestAnimationFrame(animateCube);
                }
                animateCube();
                
            } catch (error) {
                document.getElementById('output').innerHTML = `<span class="error">❌ Scene test failed: ${error.message}</span>`;
            }
        };

        window.clearOutput = function() {
            document.getElementById('output').innerHTML = 'Output cleared.';
        };
    </script>
</body>
</html>
