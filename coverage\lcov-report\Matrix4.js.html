
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Matrix4.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> Matrix4.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/329</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/19</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/215</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @fileoverview Matrix4 - 4x4 matrix class
 * <AUTHOR> Authors
 */
&nbsp;
/**
 * 4x4 matrix class for 3D transformations
 */
class Matrix4 {
<span class="fstat-no" title="function not covered" >    co</span>nstructor() {
<span class="cstat-no" title="statement not covered" >        Object.defineProperty(this, "isMatrix4", {</span>
            value: true
        });
<span class="cstat-no" title="statement not covered" >        this.elements = [</span>
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ];
<span class="cstat-no" title="statement not covered" >        if (arguments.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            console.error("THREE.Matrix4: the constructor no longer reads arguments. use .set() instead.");</span>
        }
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t(n11, n12, n13, n14, n21, n22, n23, n24, n31, n32, n33, n34, n41, n42, n43, n44) {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
<span class="cstat-no" title="statement not covered" >        te[0] = n11; <span class="cstat-no" title="statement not covered" ></span>te[4] = n12; <span class="cstat-no" title="statement not covered" ></span>te[8] = n13; <span class="cstat-no" title="statement not covered" ></span>te[12] = n14;</span>
<span class="cstat-no" title="statement not covered" >        te[1] = n21; <span class="cstat-no" title="statement not covered" ></span>te[5] = n22; <span class="cstat-no" title="statement not covered" ></span>te[9] = n23; <span class="cstat-no" title="statement not covered" ></span>te[13] = n24;</span>
<span class="cstat-no" title="statement not covered" >        te[2] = n31; <span class="cstat-no" title="statement not covered" ></span>te[6] = n32; <span class="cstat-no" title="statement not covered" ></span>te[10] = n33; <span class="cstat-no" title="statement not covered" ></span>te[14] = n34;</span>
<span class="cstat-no" title="statement not covered" >        te[3] = n41; <span class="cstat-no" title="statement not covered" ></span>te[7] = n42; <span class="cstat-no" title="statement not covered" ></span>te[11] = n43; <span class="cstat-no" title="statement not covered" ></span>te[15] = n44;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    id</span>entity() {
<span class="cstat-no" title="statement not covered" >        this.set(</span>
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        );
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>one() {
<span class="cstat-no" title="statement not covered" >        return new Matrix4().fromArray(this.elements);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    co</span>py(m) {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        const me = <span class="cstat-no" title="statement not covered" >m.elements;</span>
<span class="cstat-no" title="statement not covered" >        te[0] = me[0]; <span class="cstat-no" title="statement not covered" ></span>te[1] = me[1]; <span class="cstat-no" title="statement not covered" ></span>te[2] = me[2]; <span class="cstat-no" title="statement not covered" ></span>te[3] = me[3];</span>
<span class="cstat-no" title="statement not covered" >        te[4] = me[4]; <span class="cstat-no" title="statement not covered" ></span>te[5] = me[5]; <span class="cstat-no" title="statement not covered" ></span>te[6] = me[6]; <span class="cstat-no" title="statement not covered" ></span>te[7] = me[7];</span>
<span class="cstat-no" title="statement not covered" >        te[8] = me[8]; <span class="cstat-no" title="statement not covered" ></span>te[9] = me[9]; <span class="cstat-no" title="statement not covered" ></span>te[10] = me[10]; <span class="cstat-no" title="statement not covered" ></span>te[11] = me[11];</span>
<span class="cstat-no" title="statement not covered" >        te[12] = me[12]; <span class="cstat-no" title="statement not covered" ></span>te[13] = me[13]; <span class="cstat-no" title="statement not covered" ></span>te[14] = me[14]; <span class="cstat-no" title="statement not covered" ></span>te[15] = me[15];</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    co</span>pyPosition(m) {
        const te = <span class="cstat-no" title="statement not covered" >this.elements,</span> me = <span class="cstat-no" title="statement not covered" >m.elements;</span>
<span class="cstat-no" title="statement not covered" >        te[12] = me[12];</span>
<span class="cstat-no" title="statement not covered" >        te[13] = me[13];</span>
<span class="cstat-no" title="statement not covered" >        te[14] = me[14];</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tFromMatrix3(m) {
        const me = <span class="cstat-no" title="statement not covered" >m.elements;</span>
<span class="cstat-no" title="statement not covered" >        this.set(</span>
            me[0], me[3], me[6], 0,
            me[1], me[4], me[7], 0,
            me[2], me[5], me[8], 0,
            0, 0, 0, 1
        );
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ex</span>tractBasis(xAxis, yAxis, zAxis) {
<span class="cstat-no" title="statement not covered" >        xAxis.setFromMatrixColumn(this, 0);</span>
<span class="cstat-no" title="statement not covered" >        yAxis.setFromMatrixColumn(this, 1);</span>
<span class="cstat-no" title="statement not covered" >        zAxis.setFromMatrixColumn(this, 2);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>keBasis(xAxis, yAxis, zAxis) {
<span class="cstat-no" title="statement not covered" >        this.set(</span>
            xAxis.x, yAxis.x, zAxis.x, 0,
            xAxis.y, yAxis.y, zAxis.y, 0,
            xAxis.z, yAxis.z, zAxis.z, 0,
            0, 0, 0, 1
        );
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ex</span>tractRotation(m) {
        // this method does not support reflection matrices
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        const me = <span class="cstat-no" title="statement not covered" >m.elements;</span>
        const scaleX = <span class="cstat-no" title="statement not covered" >1 / _v1.setFromMatrixColumn(m, 0).length();</span>
        const scaleY = <span class="cstat-no" title="statement not covered" >1 / _v1.setFromMatrixColumn(m, 1).length();</span>
        const scaleZ = <span class="cstat-no" title="statement not covered" >1 / _v1.setFromMatrixColumn(m, 2).length();</span>
<span class="cstat-no" title="statement not covered" >        te[0] = me[0] * scaleX;</span>
<span class="cstat-no" title="statement not covered" >        te[1] = me[1] * scaleX;</span>
<span class="cstat-no" title="statement not covered" >        te[2] = me[2] * scaleX;</span>
<span class="cstat-no" title="statement not covered" >        te[3] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[4] = me[4] * scaleY;</span>
<span class="cstat-no" title="statement not covered" >        te[5] = me[5] * scaleY;</span>
<span class="cstat-no" title="statement not covered" >        te[6] = me[6] * scaleY;</span>
<span class="cstat-no" title="statement not covered" >        te[7] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[8] = me[8] * scaleZ;</span>
<span class="cstat-no" title="statement not covered" >        te[9] = me[9] * scaleZ;</span>
<span class="cstat-no" title="statement not covered" >        te[10] = me[10] * scaleZ;</span>
<span class="cstat-no" title="statement not covered" >        te[11] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[12] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[13] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[14] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[15] = 1;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>keRotationFromEuler(euler) {
<span class="cstat-no" title="statement not covered" >        if (!(euler &amp;&amp; euler.isEuler)) {</span>
<span class="cstat-no" title="statement not covered" >            console.error("THREE.Matrix4: .makeRotationFromEuler() now expects a Euler rotation rather than a Vector3 and order.");</span>
        }
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        const x = <span class="cstat-no" title="statement not covered" >euler.x,</span> y = <span class="cstat-no" title="statement not covered" >euler.y,</span> z = <span class="cstat-no" title="statement not covered" >euler.z;</span>
        const a = <span class="cstat-no" title="statement not covered" >Math.cos(x),</span> b = <span class="cstat-no" title="statement not covered" >Math.sin(x);</span>
        const c = <span class="cstat-no" title="statement not covered" >Math.cos(y),</span> d = <span class="cstat-no" title="statement not covered" >Math.sin(y);</span>
        const e = <span class="cstat-no" title="statement not covered" >Math.cos(z),</span> f = <span class="cstat-no" title="statement not covered" >Math.sin(z);</span>
<span class="cstat-no" title="statement not covered" >        if (euler.order === 'XYZ') {</span>
            const ae = <span class="cstat-no" title="statement not covered" >a * e,</span> af = <span class="cstat-no" title="statement not covered" >a * f,</span> be = <span class="cstat-no" title="statement not covered" >b * e,</span> bf = <span class="cstat-no" title="statement not covered" >b * f;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = c * e;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = -c * f;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = d;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = af + be * d;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = ae - bf * d;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = -b * c;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = bf - ae * d;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = be + af * d;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = a * c;</span>
        } else <span class="cstat-no" title="statement not covered" >if (euler.order === 'YXZ') {</span>
            const ce = <span class="cstat-no" title="statement not covered" >c * e,</span> cf = <span class="cstat-no" title="statement not covered" >c * f,</span> de = <span class="cstat-no" title="statement not covered" >d * e,</span> df = <span class="cstat-no" title="statement not covered" >d * f;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = ce + df * b;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = de * b - cf;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = a * d;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = a * f;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = a * e;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = -b;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = cf * b - de;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = df + ce * b;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = a * c;</span>
        } else <span class="cstat-no" title="statement not covered" >if (euler.order === 'ZXY') {</span>
            const ce = <span class="cstat-no" title="statement not covered" >c * e,</span> cf = <span class="cstat-no" title="statement not covered" >c * f,</span> de = <span class="cstat-no" title="statement not covered" >d * e,</span> df = <span class="cstat-no" title="statement not covered" >d * f;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = ce - df * b;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = -a * f;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = de + cf * b;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = cf + de * b;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = a * e;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = df - ce * b;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = -a * d;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = b;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = a * c;</span>
        } else <span class="cstat-no" title="statement not covered" >if (euler.order === 'ZYX') {</span>
            const ae = <span class="cstat-no" title="statement not covered" >a * e,</span> af = <span class="cstat-no" title="statement not covered" >a * f,</span> be = <span class="cstat-no" title="statement not covered" >b * e,</span> bf = <span class="cstat-no" title="statement not covered" >b * f;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = c * e;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = be * d - af;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = ae * d + bf;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = c * f;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = bf * d + ae;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = af * d - be;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = -d;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = b * c;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = a * c;</span>
        } else <span class="cstat-no" title="statement not covered" >if (euler.order === 'YZX') {</span>
            const ac = <span class="cstat-no" title="statement not covered" >a * c,</span> ad = <span class="cstat-no" title="statement not covered" >a * d,</span> bc = <span class="cstat-no" title="statement not covered" >b * c,</span> bd = <span class="cstat-no" title="statement not covered" >b * d;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = c * e;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = bd - ac * f;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = bc * f + ad;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = f;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = a * e;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = -b * e;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = -d * e;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = ad * f + bc;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = ac - bd * f;</span>
        } else <span class="cstat-no" title="statement not covered" >if (euler.order === 'XZY') {</span>
            const ac = <span class="cstat-no" title="statement not covered" >a * c,</span> ad = <span class="cstat-no" title="statement not covered" >a * d,</span> bc = <span class="cstat-no" title="statement not covered" >b * c,</span> bd = <span class="cstat-no" title="statement not covered" >b * d;</span>
<span class="cstat-no" title="statement not covered" >            te[0] = c * e;</span>
<span class="cstat-no" title="statement not covered" >            te[4] = -f;</span>
<span class="cstat-no" title="statement not covered" >            te[8] = d * e;</span>
<span class="cstat-no" title="statement not covered" >            te[1] = ac * f + bd;</span>
<span class="cstat-no" title="statement not covered" >            te[5] = a * e;</span>
<span class="cstat-no" title="statement not covered" >            te[9] = ad * f - bc;</span>
<span class="cstat-no" title="statement not covered" >            te[2] = bc * f - ad;</span>
<span class="cstat-no" title="statement not covered" >            te[6] = b * e;</span>
<span class="cstat-no" title="statement not covered" >            te[10] = bd * f + ac;</span>
        }
        // bottom row
<span class="cstat-no" title="statement not covered" >        te[3] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[7] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[11] = 0;</span>
        // last column
<span class="cstat-no" title="statement not covered" >        te[12] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[13] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[14] = 0;</span>
<span class="cstat-no" title="statement not covered" >        te[15] = 1;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>keRotationFromQuaternion(q) {
<span class="cstat-no" title="statement not covered" >        return this.compose(_zero, q, _one);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    lo</span>okAt(eye, target, up) {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
<span class="cstat-no" title="statement not covered" >        _z.subVectors(eye, target);</span>
<span class="cstat-no" title="statement not covered" >        if (_z.lengthSq() === 0) {</span>
            // eye and target are in the same position
<span class="cstat-no" title="statement not covered" >            _z.z = 1;</span>
        }
<span class="cstat-no" title="statement not covered" >        _z.normalize();</span>
<span class="cstat-no" title="statement not covered" >        _x.crossVectors(up, _z);</span>
<span class="cstat-no" title="statement not covered" >        if (_x.lengthSq() === 0) {</span>
            // up and z are parallel
<span class="cstat-no" title="statement not covered" >            if (Math.abs(up.z) === 1) {</span>
<span class="cstat-no" title="statement not covered" >                _z.x += 0.0001;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                _z.z += 0.0001;</span>
            }
<span class="cstat-no" title="statement not covered" >            _z.normalize();</span>
<span class="cstat-no" title="statement not covered" >            _x.crossVectors(up, _z);</span>
        }
<span class="cstat-no" title="statement not covered" >        _x.normalize();</span>
<span class="cstat-no" title="statement not covered" >        _y.crossVectors(_z, _x);</span>
<span class="cstat-no" title="statement not covered" >        te[0] = _x.x; <span class="cstat-no" title="statement not covered" ></span>te[4] = _y.x; <span class="cstat-no" title="statement not covered" ></span>te[8] = _z.x;</span>
<span class="cstat-no" title="statement not covered" >        te[1] = _x.y; <span class="cstat-no" title="statement not covered" ></span>te[5] = _y.y; <span class="cstat-no" title="statement not covered" ></span>te[9] = _z.y;</span>
<span class="cstat-no" title="statement not covered" >        te[2] = _x.z; <span class="cstat-no" title="statement not covered" ></span>te[6] = _y.z; <span class="cstat-no" title="statement not covered" ></span>te[10] = _z.z;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiply(m, n) {
<span class="cstat-no" title="statement not covered" >        if (n !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn("THREE.Matrix4: .multiply() now only accepts one argument. Use .multiplyMatrices( a, b ) instead.");</span>
<span class="cstat-no" title="statement not covered" >            return this.multiplyMatrices(m, n);</span>
        }
<span class="cstat-no" title="statement not covered" >        return this.multiplyMatrices(this, m);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    pr</span>emultiply(m) {
<span class="cstat-no" title="statement not covered" >        return this.multiplyMatrices(m, this);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiplyMatrices(a, b) {
        const ae = <span class="cstat-no" title="statement not covered" >a.elements;</span>
        const be = <span class="cstat-no" title="statement not covered" >b.elements;</span>
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        const a11 = <span class="cstat-no" title="statement not covered" >ae[0],</span> a12 = <span class="cstat-no" title="statement not covered" >ae[4],</span> a13 = <span class="cstat-no" title="statement not covered" >ae[8],</span> a14 = <span class="cstat-no" title="statement not covered" >ae[12];</span>
        const a21 = <span class="cstat-no" title="statement not covered" >ae[1],</span> a22 = <span class="cstat-no" title="statement not covered" >ae[5],</span> a23 = <span class="cstat-no" title="statement not covered" >ae[9],</span> a24 = <span class="cstat-no" title="statement not covered" >ae[13];</span>
        const a31 = <span class="cstat-no" title="statement not covered" >ae[2],</span> a32 = <span class="cstat-no" title="statement not covered" >ae[6],</span> a33 = <span class="cstat-no" title="statement not covered" >ae[10],</span> a34 = <span class="cstat-no" title="statement not covered" >ae[14];</span>
        const a41 = <span class="cstat-no" title="statement not covered" >ae[3],</span> a42 = <span class="cstat-no" title="statement not covered" >ae[7],</span> a43 = <span class="cstat-no" title="statement not covered" >ae[11],</span> a44 = <span class="cstat-no" title="statement not covered" >ae[15];</span>
        const b11 = <span class="cstat-no" title="statement not covered" >be[0],</span> b12 = <span class="cstat-no" title="statement not covered" >be[4],</span> b13 = <span class="cstat-no" title="statement not covered" >be[8],</span> b14 = <span class="cstat-no" title="statement not covered" >be[12];</span>
        const b21 = <span class="cstat-no" title="statement not covered" >be[1],</span> b22 = <span class="cstat-no" title="statement not covered" >be[5],</span> b23 = <span class="cstat-no" title="statement not covered" >be[9],</span> b24 = <span class="cstat-no" title="statement not covered" >be[13];</span>
        const b31 = <span class="cstat-no" title="statement not covered" >be[2],</span> b32 = <span class="cstat-no" title="statement not covered" >be[6],</span> b33 = <span class="cstat-no" title="statement not covered" >be[10],</span> b34 = <span class="cstat-no" title="statement not covered" >be[14];</span>
        const b41 = <span class="cstat-no" title="statement not covered" >be[3],</span> b42 = <span class="cstat-no" title="statement not covered" >be[7],</span> b43 = <span class="cstat-no" title="statement not covered" >be[11],</span> b44 = <span class="cstat-no" title="statement not covered" >be[15];</span>
<span class="cstat-no" title="statement not covered" >        te[0] = a11 * b11 + a12 * b21 + a13 * b31 + a14 * b41;</span>
<span class="cstat-no" title="statement not covered" >        te[4] = a11 * b12 + a12 * b22 + a13 * b32 + a14 * b42;</span>
<span class="cstat-no" title="statement not covered" >        te[8] = a11 * b13 + a12 * b23 + a13 * b33 + a14 * b43;</span>
<span class="cstat-no" title="statement not covered" >        te[12] = a11 * b14 + a12 * b24 + a13 * b34 + a14 * b44;</span>
<span class="cstat-no" title="statement not covered" >        te[1] = a21 * b11 + a22 * b21 + a23 * b31 + a24 * b41;</span>
<span class="cstat-no" title="statement not covered" >        te[5] = a21 * b12 + a22 * b22 + a23 * b32 + a24 * b42;</span>
<span class="cstat-no" title="statement not covered" >        te[9] = a21 * b13 + a22 * b23 + a23 * b33 + a24 * b43;</span>
<span class="cstat-no" title="statement not covered" >        te[13] = a21 * b14 + a22 * b24 + a23 * b34 + a24 * b44;</span>
<span class="cstat-no" title="statement not covered" >        te[2] = a31 * b11 + a32 * b21 + a33 * b31 + a34 * b41;</span>
<span class="cstat-no" title="statement not covered" >        te[6] = a31 * b12 + a32 * b22 + a33 * b32 + a34 * b42;</span>
<span class="cstat-no" title="statement not covered" >        te[10] = a31 * b13 + a32 * b23 + a33 * b33 + a34 * b43;</span>
<span class="cstat-no" title="statement not covered" >        te[14] = a31 * b14 + a32 * b24 + a33 * b34 + a34 * b44;</span>
<span class="cstat-no" title="statement not covered" >        te[3] = a41 * b11 + a42 * b21 + a43 * b31 + a44 * b41;</span>
<span class="cstat-no" title="statement not covered" >        te[7] = a41 * b12 + a42 * b22 + a43 * b32 + a44 * b42;</span>
<span class="cstat-no" title="statement not covered" >        te[11] = a41 * b13 + a42 * b23 + a43 * b33 + a44 * b43;</span>
<span class="cstat-no" title="statement not covered" >        te[15] = a41 * b14 + a42 * b24 + a43 * b34 + a44 * b44;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiplyScalar(s) {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
<span class="cstat-no" title="statement not covered" >        te[0] *= s; <span class="cstat-no" title="statement not covered" ></span>te[4] *= s; <span class="cstat-no" title="statement not covered" ></span>te[8] *= s; <span class="cstat-no" title="statement not covered" ></span>te[12] *= s;</span>
<span class="cstat-no" title="statement not covered" >        te[1] *= s; <span class="cstat-no" title="statement not covered" ></span>te[5] *= s; <span class="cstat-no" title="statement not covered" ></span>te[9] *= s; <span class="cstat-no" title="statement not covered" ></span>te[13] *= s;</span>
<span class="cstat-no" title="statement not covered" >        te[2] *= s; <span class="cstat-no" title="statement not covered" ></span>te[6] *= s; <span class="cstat-no" title="statement not covered" ></span>te[10] *= s; <span class="cstat-no" title="statement not covered" ></span>te[14] *= s;</span>
<span class="cstat-no" title="statement not covered" >        te[3] *= s; <span class="cstat-no" title="statement not covered" ></span>te[7] *= s; <span class="cstat-no" title="statement not covered" ></span>te[11] *= s; <span class="cstat-no" title="statement not covered" ></span>te[15] *= s;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    de</span>terminant() {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        const n11 = <span class="cstat-no" title="statement not covered" >te[0],</span> n12 = <span class="cstat-no" title="statement not covered" >te[4],</span> n13 = <span class="cstat-no" title="statement not covered" >te[8],</span> n14 = <span class="cstat-no" title="statement not covered" >te[12];</span>
        const n21 = <span class="cstat-no" title="statement not covered" >te[1],</span> n22 = <span class="cstat-no" title="statement not covered" >te[5],</span> n23 = <span class="cstat-no" title="statement not covered" >te[9],</span> n24 = <span class="cstat-no" title="statement not covered" >te[13];</span>
        const n31 = <span class="cstat-no" title="statement not covered" >te[2],</span> n32 = <span class="cstat-no" title="statement not covered" >te[6],</span> n33 = <span class="cstat-no" title="statement not covered" >te[10],</span> n34 = <span class="cstat-no" title="statement not covered" >te[14];</span>
        const n41 = <span class="cstat-no" title="statement not covered" >te[3],</span> n42 = <span class="cstat-no" title="statement not covered" >te[7],</span> n43 = <span class="cstat-no" title="statement not covered" >te[11],</span> n44 = <span class="cstat-no" title="statement not covered" >te[15];</span>
        //TODO: make this more efficient
        //( based on http://www.euclideanspace.com/maths/algebra/matrix/functions/inverse/fourD/index.htm )
<span class="cstat-no" title="statement not covered" >        return (</span>
            n41 * (
                +n14 * n23 * n32
                - n13 * n24 * n32
                - n14 * n22 * n33
                + n12 * n24 * n33
                + n13 * n22 * n34
                - n12 * n23 * n34
            ) +
            n42 * (
                +n11 * n23 * n34
                - n11 * n24 * n33
                + n14 * n21 * n33
                - n13 * n21 * n34
                + n13 * n24 * n31
                - n14 * n23 * n31
            ) +
            n43 * (
                +n11 * n24 * n32
                - n11 * n22 * n34
                - n14 * n21 * n32
                + n12 * n21 * n34
                + n14 * n22 * n31
                - n12 * n24 * n31
            ) +
            n44 * (
                -n13 * n22 * n31
                - n11 * n23 * n32
                + n11 * n22 * n33
                + n13 * n21 * n32
                - n12 * n21 * n33
                + n12 * n23 * n31
            )
        );
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    tr</span>anspose() {
        const te = <span class="cstat-no" title="statement not covered" >this.elements;</span>
        let tmp;
<span class="cstat-no" title="statement not covered" >        tmp = te[1]; <span class="cstat-no" title="statement not covered" ></span>te[1] = te[4]; <span class="cstat-no" title="statement not covered" ></span>te[4] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        tmp = te[2]; <span class="cstat-no" title="statement not covered" ></span>te[2] = te[8]; <span class="cstat-no" title="statement not covered" ></span>te[8] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        tmp = te[6]; <span class="cstat-no" title="statement not covered" ></span>te[6] = te[9]; <span class="cstat-no" title="statement not covered" ></span>te[9] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        tmp = te[3]; <span class="cstat-no" title="statement not covered" ></span>te[3] = te[12]; <span class="cstat-no" title="statement not covered" ></span>te[12] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        tmp = te[7]; <span class="cstat-no" title="statement not covered" ></span>te[7] = te[13]; <span class="cstat-no" title="statement not covered" ></span>te[13] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        tmp = te[11]; <span class="cstat-no" title="statement not covered" ></span>te[11] = te[14]; <span class="cstat-no" title="statement not covered" ></span>te[14] = tmp;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
}
&nbsp;
// Temporary variables to avoid allocations
const _v1 = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
const _zero = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
const _one = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
const _x = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
const _y = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
const _z = <span class="cstat-no" title="statement not covered" >{};</span> // Will be set when Vector3 is available
&nbsp;
export { Matrix4 };
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-04T21:07:49.701Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    