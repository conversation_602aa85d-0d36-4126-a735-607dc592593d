// *********************************************************************************************
// This file is manually-edited by diffing against an autogenerated file. See README.md.
// *********************************************************************************************

// *********************************************************************************************
// Manually-written
// *********************************************************************************************

// AllowSharedBufferSource wasn't introduced until TypeScript 5.2.
// But it also didn't include SharedArrayBuffer in the union.
// This broke in ES2024 when ArrayBuffer gained some properties that SharedArrayBuffer didn't.
// So, we use our own definition for AllowSharedBufferSource.

type GPUAllowSharedBufferSource =

    | BufferSource
    | SharedArrayBuffer;

// Stronger typing for getContext()

interface HTMLCanvasElement {
  getContext(
    contextId:
      | "webgpu"
  ): GPUCanvasContext | null;
}
interface OffscreenCanvas {
  getContext(
    contextId:
      | "webgpu"
  ): GPUCanvasContext | null;
}

// Defined as an empty interface here to prevent errors when using these types in a worker.

interface HTMLVideoElement {}

// Strict types defined to help developers catch a common class of errors.
// This interface defines depth as an undefined, which will cause a type check failure if someone
// attempts to set depth rather than depthOrArrayLayers on a GPUExtent3D (an easy mistake to make.)

type GPUOrigin2DStrict =

    | Iterable<GPUIntegerCoordinate>
    | GPUOrigin2DDictStrict;
interface GPUOrigin2DDictStrict
  extends GPUOrigin2DDict {
  /** @deprecated Does not exist for GPUOrigin2D. */
  z?: undefined;
}
type GPUExtent3DStrict =

    | Iterable<GPUIntegerCoordinate>
    | GPUExtent3DDictStrict;
interface GPUExtent3DDictStrict
  extends GPUExtent3DDict {
  /** @deprecated The correct name is `depthOrArrayLayers`. */
  depth?: undefined;
}

// Stronger typing for event listeners.

/** @internal */
interface __GPUDeviceEventMap {
  uncapturederror: GPUUncapturedErrorEvent;
}

// Extensions to the generated definition below.
interface GPUDevice {
  addEventListener<
    K extends keyof __GPUDeviceEventMap
  >(
    type: K,
    listener: (
      this: GPUDevice,
      ev: __GPUDeviceEventMap[K]
    ) => any,
    options?:
      | boolean
      | AddEventListenerOptions
  ): void;
  addEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?:
      | boolean
      | AddEventListenerOptions
  ): void;
  removeEventListener<
    K extends keyof __GPUDeviceEventMap
  >(
    type: K,
    listener: (
      this: GPUDevice,
      ev: __GPUDeviceEventMap[K]
    ) => any,
    options?:
      | boolean
      | EventListenerOptions
  ): void;
  removeEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?:
      | boolean
      | EventListenerOptions
  ): void;
}

// *********************************************************************************************
// Semi-auto-generated (by manual diff with autogenerated types)
// *********************************************************************************************

type GPUBindingResource =

    | GPUSampler
    | GPUTextureView
    | GPUBuffer
    | GPUBufferBinding
    | GPUExternalTexture;
type GPUBufferDynamicOffset =
  number;
type GPUBufferUsageFlags =
  number;
type GPUColor =

    | Iterable<number>
    | GPUColorDict;
type GPUColorWriteFlags =
  number;
type GPUCopyExternalImageSource =

    | ImageBitmap
    | ImageData
    | HTMLImageElement
    | HTMLVideoElement
    | VideoFrame
    | HTMLCanvasElement
    | OffscreenCanvas;
type GPUDepthBias =
  number;
type GPUExtent3D =

    | Iterable<GPUIntegerCoordinate>
    | GPUExtent3DDict;
type GPUFlagsConstant =
  number;
type GPUIndex32 =
  number;
type GPUIntegerCoordinate =
  number;
type GPUIntegerCoordinateOut =
  number;
type GPUMapModeFlags =
  number;
type GPUOrigin2D =

    | Iterable<GPUIntegerCoordinate>
    | GPUOrigin2DDict;
type GPUOrigin3D =

    | Iterable<GPUIntegerCoordinate>
    | GPUOrigin3DDict;
type GPUPipelineConstantValue =
  number;
type GPUSampleMask =
  number;
type GPUShaderStageFlags =
  number;
type GPUSignedOffset32 =
  number;
type GPUSize32 =
  number;
type GPUSize32Out =
  number;
type GPUSize64 =
  number;
type GPUSize64Out =
  number;
type GPUStencilValue =
  number;
type GPUTextureUsageFlags =
  number;
type GPUAddressMode =

    | "clamp-to-edge"
    | "repeat"
    | "mirror-repeat";
type GPUAutoLayoutMode =
  "auto";
type GPUBlendFactor =

    | "zero"
    | "one"
    | "src"
    | "one-minus-src"
    | "src-alpha"
    | "one-minus-src-alpha"
    | "dst"
    | "one-minus-dst"
    | "dst-alpha"
    | "one-minus-dst-alpha"
    | "src-alpha-saturated"
    | "constant"
    | "one-minus-constant"
    | "src1"
    | "one-minus-src1"
    | "src1-alpha"
    | "one-minus-src1-alpha";
type GPUBlendOperation =

    | "add"
    | "subtract"
    | "reverse-subtract"
    | "min"
    | "max";
type GPUBufferBindingType =

    | "uniform"
    | "storage"
    | "read-only-storage";
type GPUBufferMapState =

    | "unmapped"
    | "pending"
    | "mapped";
type GPUCanvasAlphaMode =

    | "opaque"
    | "premultiplied";
type GPUCanvasToneMappingMode =

    | "standard"
    | "extended";
type GPUCompareFunction =

    | "never"
    | "less"
    | "equal"
    | "less-equal"
    | "greater"
    | "not-equal"
    | "greater-equal"
    | "always";
type GPUCompilationMessageType =

    | "error"
    | "warning"
    | "info";
type GPUCullMode =

    | "none"
    | "front"
    | "back";
type GPUDeviceLostReason =

    | "unknown"
    | "destroyed";
type GPUErrorFilter =

    | "validation"
    | "out-of-memory"
    | "internal";
type GPUFeatureName =

    | "core-features-and-limits"
    | "depth-clip-control"
    | "depth32float-stencil8"
    | "texture-compression-bc"
    | "texture-compression-bc-sliced-3d"
    | "texture-compression-etc2"
    | "texture-compression-astc"
    | "texture-compression-astc-sliced-3d"
    | "timestamp-query"
    | "indirect-first-instance"
    | "shader-f16"
    | "rg11b10ufloat-renderable"
    | "bgra8unorm-storage"
    | "float32-filterable"
    | "float32-blendable"
    | "clip-distances"
    | "dual-source-blending"
    | "subgroups"
    | "texture-formats-tier1"
    | "texture-formats-tier2";
type GPUFilterMode =

    | "nearest"
    | "linear";
type GPUFrontFace =

    | "ccw"
    | "cw";
type GPUIndexFormat =

    | "uint16"
    | "uint32";
type GPULoadOp =

    | "load"
    | "clear";
type GPUMipmapFilterMode =

    | "nearest"
    | "linear";
type GPUPipelineErrorReason =

    | "validation"
    | "internal";
type GPUPowerPreference =

    | "low-power"
    | "high-performance";
type GPUPrimitiveTopology =

    | "point-list"
    | "line-list"
    | "line-strip"
    | "triangle-list"
    | "triangle-strip";
type GPUQueryType =

    | "occlusion"
    | "timestamp";
type GPUSamplerBindingType =

    | "filtering"
    | "non-filtering"
    | "comparison";
type GPUStencilOperation =

    | "keep"
    | "zero"
    | "replace"
    | "invert"
    | "increment-clamp"
    | "decrement-clamp"
    | "increment-wrap"
    | "decrement-wrap";
type GPUStorageTextureAccess =

    | "write-only"
    | "read-only"
    | "read-write";
type GPUStoreOp =

    | "store"
    | "discard";
type GPUTextureAspect =

    | "all"
    | "stencil-only"
    | "depth-only";
type GPUTextureDimension =

    | "1d"
    | "2d"
    | "3d";
type GPUTextureFormat =

    | "r8unorm"
    | "r8snorm"
    | "r8uint"
    | "r8sint"
    | "r16unorm"
    | "r16snorm"
    | "r16uint"
    | "r16sint"
    | "r16float"
    | "rg8unorm"
    | "rg8snorm"
    | "rg8uint"
    | "rg8sint"
    | "r32uint"
    | "r32sint"
    | "r32float"
    | "rg16unorm"
    | "rg16snorm"
    | "rg16uint"
    | "rg16sint"
    | "rg16float"
    | "rgba8unorm"
    | "rgba8unorm-srgb"
    | "rgba8snorm"
    | "rgba8uint"
    | "rgba8sint"
    | "bgra8unorm"
    | "bgra8unorm-srgb"
    | "rgb9e5ufloat"
    | "rgb10a2uint"
    | "rgb10a2unorm"
    | "rg11b10ufloat"
    | "rg32uint"
    | "rg32sint"
    | "rg32float"
    | "rgba16unorm"
    | "rgba16snorm"
    | "rgba16uint"
    | "rgba16sint"
    | "rgba16float"
    | "rgba32uint"
    | "rgba32sint"
    | "rgba32float"
    | "stencil8"
    | "depth16unorm"
    | "depth24plus"
    | "depth24plus-stencil8"
    | "depth32float"
    | "depth32float-stencil8"
    | "bc1-rgba-unorm"
    | "bc1-rgba-unorm-srgb"
    | "bc2-rgba-unorm"
    | "bc2-rgba-unorm-srgb"
    | "bc3-rgba-unorm"
    | "bc3-rgba-unorm-srgb"
    | "bc4-r-unorm"
    | "bc4-r-snorm"
    | "bc5-rg-unorm"
    | "bc5-rg-snorm"
    | "bc6h-rgb-ufloat"
    | "bc6h-rgb-float"
    | "bc7-rgba-unorm"
    | "bc7-rgba-unorm-srgb"
    | "etc2-rgb8unorm"
    | "etc2-rgb8unorm-srgb"
    | "etc2-rgb8a1unorm"
    | "etc2-rgb8a1unorm-srgb"
    | "etc2-rgba8unorm"
    | "etc2-rgba8unorm-srgb"
    | "eac-r11unorm"
    | "eac-r11snorm"
    | "eac-rg11unorm"
    | "eac-rg11snorm"
    | "astc-4x4-unorm"
    | "astc-4x4-unorm-srgb"
    | "astc-5x4-unorm"
    | "astc-5x4-unorm-srgb"
    | "astc-5x5-unorm"
    | "astc-5x5-unorm-srgb"
    | "astc-6x5-unorm"
    | "astc-6x5-unorm-srgb"
    | "astc-6x6-unorm"
    | "astc-6x6-unorm-srgb"
    | "astc-8x5-unorm"
    | "astc-8x5-unorm-srgb"
    | "astc-8x6-unorm"
    | "astc-8x6-unorm-srgb"
    | "astc-8x8-unorm"
    | "astc-8x8-unorm-srgb"
    | "astc-10x5-unorm"
    | "astc-10x5-unorm-srgb"
    | "astc-10x6-unorm"
    | "astc-10x6-unorm-srgb"
    | "astc-10x8-unorm"
    | "astc-10x8-unorm-srgb"
    | "astc-10x10-unorm"
    | "astc-10x10-unorm-srgb"
    | "astc-12x10-unorm"
    | "astc-12x10-unorm-srgb"
    | "astc-12x12-unorm"
    | "astc-12x12-unorm-srgb";
type GPUTextureSampleType =

    | "float"
    | "unfilterable-float"
    | "depth"
    | "sint"
    | "uint";
type GPUTextureViewDimension =

    | "1d"
    | "2d"
    | "2d-array"
    | "cube"
    | "cube-array"
    | "3d";
type GPUVertexFormat =

    | "uint8"
    | "uint8x2"
    | "uint8x4"
    | "sint8"
    | "sint8x2"
    | "sint8x4"
    | "unorm8"
    | "unorm8x2"
    | "unorm8x4"
    | "snorm8"
    | "snorm8x2"
    | "snorm8x4"
    | "uint16"
    | "uint16x2"
    | "uint16x4"
    | "sint16"
    | "sint16x2"
    | "sint16x4"
    | "unorm16"
    | "unorm16x2"
    | "unorm16x4"
    | "snorm16"
    | "snorm16x2"
    | "snorm16x4"
    | "float16"
    | "float16x2"
    | "float16x4"
    | "float32"
    | "float32x2"
    | "float32x3"
    | "float32x4"
    | "uint32"
    | "uint32x2"
    | "uint32x3"
    | "uint32x4"
    | "sint32"
    | "sint32x2"
    | "sint32x3"
    | "sint32x4"
    | "unorm10-10-10-2"
    | "unorm8x4-bgra";
type GPUVertexStepMode =

    | "vertex"
    | "instance";

interface GPUBindGroupDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The {@link GPUBindGroupLayout} the entries of this bind group will conform to.
   */
  layout: GPUBindGroupLayout;
  /**
   * A list of entries describing the resources to expose to the shader for each binding
   * described by the {@link GPUBindGroupDescriptor#layout}.
   */
  entries: Iterable<GPUBindGroupEntry>;
}

interface GPUBindGroupEntry {
  /**
   * A unique identifier for a resource binding within the {@link GPUBindGroup}, corresponding to a
   * {@link GPUBindGroupLayoutEntry#binding | GPUBindGroupLayoutEntry.binding} and a @binding
   * attribute in the {@link GPUShaderModule}.
   */
  binding: GPUIndex32;
  /**
   * The resource to bind, which may be a {@link GPUSampler}, {@link GPUTextureView},
   * {@link GPUBuffer}, {@link GPUBufferBinding}, or {@link GPUExternalTexture}.
   */
  resource: GPUBindingResource;
}

interface GPUBindGroupLayoutDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * A list of entries describing the shader resource bindings for a bind group.
   */
  entries: Iterable<GPUBindGroupLayoutEntry>;
}

interface GPUBindGroupLayoutEntry {
  /**
   * A unique identifier for a resource binding within the {@link GPUBindGroupLayout}, corresponding
   * to a {@link GPUBindGroupEntry#binding | GPUBindGroupEntry.binding} and a @binding
   * attribute in the {@link GPUShaderModule}.
   */
  binding: GPUIndex32;
  /**
   * A bitset of the members of {@link GPUShaderStage}.
   * Each set bit indicates that a {@link GPUBindGroupLayoutEntry}'s resource
   * will be accessible from the associated shader stage.
   */
  visibility: GPUShaderStageFlags;
  /**
   * When map/exist|provided, indicates the binding resource type for this {@link GPUBindGroupLayoutEntry}
   * is {@link GPUBufferBinding}.
   */
  buffer?: GPUBufferBindingLayout;
  /**
   * When map/exist|provided, indicates the binding resource type for this {@link GPUBindGroupLayoutEntry}
   * is {@link GPUSampler}.
   */
  sampler?: GPUSamplerBindingLayout;
  /**
   * When map/exist|provided, indicates the binding resource type for this {@link GPUBindGroupLayoutEntry}
   * is {@link GPUTextureView}.
   */
  texture?: GPUTextureBindingLayout;
  /**
   * When map/exist|provided, indicates the binding resource type for this {@link GPUBindGroupLayoutEntry}
   * is {@link GPUTextureView}.
   */
  storageTexture?: GPUStorageTextureBindingLayout;
  /**
   * When map/exist|provided, indicates the binding resource type for this {@link GPUBindGroupLayoutEntry}
   * is either {@link GPUExternalTexture} or {@link GPUTextureView}.
   * External textures use several binding slots: see Exceeds the binding slot limits.
   */
  externalTexture?: GPUExternalTextureBindingLayout;
}

interface GPUBlendComponent {
  /**
   * Defines the {@link GPUBlendOperation} used to calculate the values written to the target
   * attachment components.
   */
  operation?: GPUBlendOperation;
  /**
   * Defines the {@link GPUBlendFactor} operation to be performed on values from the fragment shader.
   */
  srcFactor?: GPUBlendFactor;
  /**
   * Defines the {@link GPUBlendFactor} operation to be performed on values from the target attachment.
   */
  dstFactor?: GPUBlendFactor;
}

interface GPUBlendState {
  /**
   * Defines the blending behavior of the corresponding render target for color channels.
   */
  color: GPUBlendComponent;
  /**
   * Defines the blending behavior of the corresponding render target for the alpha channel.
   */
  alpha: GPUBlendComponent;
}

interface GPUBufferBinding {
  /**
   * The {@link GPUBuffer} to bind.
   */
  buffer: GPUBuffer;
  /**
   * The offset, in bytes, from the beginning of {@link GPUBufferBinding#buffer} to the
   * beginning of the range exposed to the shader by the buffer binding.
   */
  offset?: GPUSize64;
  /**
   * The size, in bytes, of the buffer binding.
   * If not map/exist|provided, specifies the range starting at
   * {@link GPUBufferBinding#offset} and ending at the end of {@link GPUBufferBinding#buffer}.
   */
  size?: GPUSize64;
}

interface GPUBufferBindingLayout {
  /**
   * Indicates the type required for buffers bound to this bindings.
   */
  type?: GPUBufferBindingType;
  /**
   * Indicates whether this binding requires a dynamic offset.
   */
  hasDynamicOffset?: boolean;
  /**
   * Indicates the minimum {@link GPUBufferBinding#size} of a buffer binding used with this bind point.
   * Bindings are always validated against this size in {@link GPUDevice#createBindGroup}.
   * If this *is not* `0`, pipeline creation additionally [$validating shader binding|validates$]
   * that this value &ge; the minimum buffer binding size of the variable.
   * If this *is* `0`, it is ignored by pipeline creation, and instead draw/dispatch commands
   * [$Validate encoder bind groups|validate$] that each binding in the {@link GPUBindGroup}
   * satisfies the minimum buffer binding size of the variable.
   * Note:
   * Similar execution-time validation is theoretically possible for other
   * binding-related fields specified for early validation, like
   * {@link GPUTextureBindingLayout#sampleType} and {@link GPUStorageTextureBindingLayout#format},
   * which currently can only be validated in pipeline creation.
   * However, such execution-time validation could be costly or unnecessarily complex, so it is
   * available only for {@link GPUBufferBindingLayout#minBindingSize} which is expected to have the
   * most ergonomic impact.
   */
  minBindingSize?: GPUSize64;
}

interface GPUBufferDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The size of the buffer in bytes.
   */
  size: GPUSize64;
  /**
   * The allowed usages for the buffer.
   */
  usage: GPUBufferUsageFlags;
  /**
   * If `true` creates the buffer in an already mapped state, allowing
   * {@link GPUBuffer#getMappedRange} to be called immediately. It is valid to set
   * {@link GPUBufferDescriptor#mappedAtCreation} to `true` even if {@link GPUBufferDescriptor#usage}
   * does not contain {@link GPUBufferUsage#MAP_READ} or {@link GPUBufferUsage#MAP_WRITE}. This can be
   * used to set the buffer's initial data.
   * Guarantees that even if the buffer creation eventually fails, it will still appear as if the
   * mapped range can be written/read to until it is unmapped.
   */
  mappedAtCreation?: boolean;
}

interface GPUCanvasConfiguration {
  /**
   * The {@link GPUDevice} that textures returned by {@link GPUCanvasContext#getCurrentTexture} will be
   * compatible with.
   */
  device: GPUDevice;
  /**
   * The format that textures returned by {@link GPUCanvasContext#getCurrentTexture} will have.
   * Must be one of the Supported context formats.
   */
  format: GPUTextureFormat;
  /**
   * The usage that textures returned by {@link GPUCanvasContext#getCurrentTexture} will have.
   * {@link GPUTextureUsage#RENDER_ATTACHMENT} is the default, but is not automatically included
   * if the usage is explicitly set. Be sure to include {@link GPUTextureUsage#RENDER_ATTACHMENT}
   * when setting a custom usage if you wish to use textures returned by
   * {@link GPUCanvasContext#getCurrentTexture} as color targets for a render pass.
   */
  usage?: GPUTextureUsageFlags;
  /**
   * The formats that views created from textures returned by
   * {@link GPUCanvasContext#getCurrentTexture} may use.
   */
  viewFormats?: Iterable<GPUTextureFormat>;
  /**
   * The color space that values written into textures returned by
   * {@link GPUCanvasContext#getCurrentTexture} should be displayed with.
   */
  colorSpace?: PredefinedColorSpace;
  /**
   * The tone mapping determines how the content of textures returned by
   * {@link GPUCanvasContext#getCurrentTexture} are to be displayed.
   * Note: If an implementation doesn't support HDR WebGPU canvases, it should also not expose this member, to allow for feature detection. See {@link GPUCanvasContext#getConfiguration}.
   */
  toneMapping?: GPUCanvasToneMapping;
  /**
   * Determines the effect that alpha values will have on the content of textures returned by
   * {@link GPUCanvasContext#getCurrentTexture} when read, displayed, or used as an image source.
   */
  alphaMode?: GPUCanvasAlphaMode;
}

interface GPUCanvasConfigurationOut
  extends Required<
    Omit<
      GPUCanvasConfiguration,
      "toneMapping"
    >
  > {
  /** {@inheritDoc GPUCanvasConfiguration.viewFormats} */
  viewFormats: GPUTextureFormat[];
  /**
   * {@inheritDoc GPUCanvasConfiguration.toneMapping}
   */
  toneMapping?: GPUCanvasToneMapping;
}

interface GPUCanvasToneMapping {
  mode?: GPUCanvasToneMappingMode;
}

interface GPUColorDict {
  /**
   * The red channel value.
   */
  r: number;
  /**
   * The green channel value.
   */
  g: number;
  /**
   * The blue channel value.
   */
  b: number;
  /**
   * The alpha channel value.
   */
  a: number;
}

interface GPUColorTargetState {
  /**
   * The {@link GPUTextureFormat} of this color target. The pipeline will only be compatible with
   * {@link GPURenderPassEncoder}s which use a {@link GPUTextureView} of this format in the
   * corresponding color attachment.
   */
  format: GPUTextureFormat;
  /**
   * The blending behavior for this color target. If left undefined, disables blending for this
   * color target.
   */
  blend?: GPUBlendState;
  /**
   * Bitmask controlling which channels are are written to when drawing to this color target.
   */
  writeMask?: GPUColorWriteFlags;
}

type GPUCommandBufferDescriptor =
  GPUObjectDescriptorBase;
type GPUCommandEncoderDescriptor =
  GPUObjectDescriptorBase;

interface GPUComputePassDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * Defines which timestamp values will be written for this pass, and where to write them to.
   */
  timestampWrites?: GPUComputePassTimestampWrites;
}

interface GPUComputePassTimestampWrites {
  /**
   * The {@link GPUQuerySet}, of type {@link GPUQueryType} `"timestamp"`, that the query results will be
   * written to.
   */
  querySet: GPUQuerySet;
  /**
   * If defined, indicates the query index in {@link GPURenderPassTimestampWrites#querySet} into
   * which the timestamp at the beginning of the compute pass will be written.
   */
  beginningOfPassWriteIndex?: GPUSize32;
  /**
   * If defined, indicates the query index in {@link GPURenderPassTimestampWrites#querySet} into
   * which the timestamp at the end of the compute pass will be written.
   */
  endOfPassWriteIndex?: GPUSize32;
}

interface GPUComputePipelineDescriptor
  extends GPUPipelineDescriptorBase {
  /**
   * Describes the compute shader entry point of the pipeline.
   */
  compute: GPUProgrammableStage;
}

interface GPUCopyExternalImageDestInfo
  extends GPUTexelCopyTextureInfo {
  /**
   * Describes the color space and encoding used to encode data into the destination texture.
   * This {@link https://www.w3.org/TR/webgpu/#color-space-conversions | may result} in values outside of the range [0, 1]
   * being written to the target texture, if its format can represent them.
   * Otherwise, the results are clamped to the target texture format's range.
   * Note:
   * If {@link GPUCopyExternalImageDestInfo#colorSpace} matches the source image,
   * conversion may not be necessary. See {@link https://www.w3.org/TR/webgpu/#color-space-conversion-elision}.
   */
  colorSpace?: PredefinedColorSpace;
  /**
   * Describes whether the data written into the texture should have its RGB channels
   * premultiplied by the alpha channel, or not.
   * If this option is set to `true` and the {@link GPUCopyExternalImageSourceInfo#source} is also
   * premultiplied, the source RGB values must be preserved even if they exceed their
   * corresponding alpha values.
   * Note:
   * If {@link GPUCopyExternalImageDestInfo#premultipliedAlpha} matches the source image,
   * conversion may not be necessary. See {@link https://www.w3.org/TR/webgpu/#color-space-conversion-elision}.
   */
  premultipliedAlpha?: boolean;
}

interface GPUCopyExternalImageSourceInfo {
  /**
   * The source of the texel copy. The copy source data is captured at the moment that
   * {@link GPUQueue#copyExternalImageToTexture} is issued. Source size is determined as described
   * by the external source dimensions table.
   */
  source: GPUCopyExternalImageSource;
  /**
   * Defines the origin of the copy - the minimum (top-left) corner of the source sub-region to copy from.
   * Together with `copySize`, defines the full copy sub-region.
   */
  origin?: GPUOrigin2D;
  /**
   * Describes whether the source image is vertically flipped, or not.
   * If this option is set to `true`, the copy is flipped vertically: the bottom row of the source
   * region is copied into the first row of the destination region, and so on.
   * The {@link GPUCopyExternalImageSourceInfo#origin} option is still relative to the top-left corner
   * of the source image, increasing downward.
   */
  flipY?: boolean;
}

interface GPUDepthStencilState {
  /**
   * The {@link GPUTextureViewDescriptor#format} of {@link GPURenderPassDescriptor#depthStencilAttachment}
   * this {@link GPURenderPipeline} will be compatible with.
   */
  format: GPUTextureFormat;
  /**
   * Indicates if this {@link GPURenderPipeline} can modify
   * {@link GPURenderPassDescriptor#depthStencilAttachment} depth values.
   */
  depthWriteEnabled?: boolean;
  /**
   * The comparison operation used to test fragment depths against
   * {@link GPURenderPassDescriptor#depthStencilAttachment} depth values.
   */
  depthCompare?: GPUCompareFunction;
  /**
   * Defines how stencil comparisons and operations are performed for front-facing primitives.
   */
  stencilFront?: GPUStencilFaceState;
  /**
   * Defines how stencil comparisons and operations are performed for back-facing primitives.
   */
  stencilBack?: GPUStencilFaceState;
  /**
   * Bitmask controlling which {@link GPURenderPassDescriptor#depthStencilAttachment} stencil value
   * bits are read when performing stencil comparison tests.
   */
  stencilReadMask?: GPUStencilValue;
  /**
   * Bitmask controlling which {@link GPURenderPassDescriptor#depthStencilAttachment} stencil value
   * bits are written to when performing stencil operations.
   */
  stencilWriteMask?: GPUStencilValue;
  /**
   * Constant depth bias added to each triangle fragment. See [$biased fragment depth$] for details.
   */
  depthBias?: GPUDepthBias;
  /**
   * Depth bias that scales with the triangle fragment’s slope. See [$biased fragment depth$] for details.
   */
  depthBiasSlopeScale?: number;
  /**
   * The maximum depth bias of a triangle fragment. See [$biased fragment depth$] for details.
   */
  depthBiasClamp?: number;
}

interface GPUDeviceDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * Specifies the features that are required by the device request.
   * The request will fail if the adapter cannot provide these features.
   * Exactly the specified set of features, and no more or less, will be allowed in validation
   * of API calls on the resulting device.
   */
  requiredFeatures?: Iterable<GPUFeatureName>;
  /**
   * Specifies the limits that are required by the device request.
   * The request will fail if the adapter cannot provide these limits.
   * Each key with a non-`undefined` value must be the name of a member of supported limits.
   * API calls on the resulting device perform validation according to the exact limits of the
   * device (not the adapter; see {@link https://www.w3.org/TR/webgpu/#limits}).
   * <!-- If we ever need limit types other than GPUSize32/GPUSize64, we can change the value
   * type to `double` or `any` in the future and write out the type conversion explicitly (by
   * reference to WebIDL spec). Or change the entire type to `any` and add back a `dictionary
   * GPULimits` and define the conversion of the whole object by reference to WebIDL. -->
   */
  requiredLimits?: Record<
    string,
    | GPUSize64
    | undefined
  >;
  /**
   * The descriptor for the default {@link GPUQueue}.
   */
  defaultQueue?: GPUQueueDescriptor;
}

interface GPUExtent3DDict {
  /**
   * The width of the extent.
   */
  width: GPUIntegerCoordinate;
  /**
   * The height of the extent.
   */
  height?: GPUIntegerCoordinate;
  /**
   * The depth of the extent or the number of array layers it contains.
   * If used with a {@link GPUTexture} with a {@link GPUTextureDimension} of {@link GPUTextureDimension} `"3d"`
   * defines the depth of the texture. If used with a {@link GPUTexture} with a {@link GPUTextureDimension}
   * of {@link GPUTextureDimension} `"2d"` defines the number of array layers in the texture.
   */
  depthOrArrayLayers?: GPUIntegerCoordinate;
}

interface GPUExternalTextureBindingLayout {}

interface GPUExternalTextureDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The video source to import the external texture from. Source size is determined as described
   * by the external source dimensions table.
   */
  source:
    | HTMLVideoElement
    | VideoFrame;
  /**
   * The color space the image contents of {@link GPUExternalTextureDescriptor#source} will be
   * converted into when reading.
   */
  colorSpace?: PredefinedColorSpace;
}

interface GPUFragmentState
  extends GPUProgrammableStage {
  /**
   * A list of {@link GPUColorTargetState} defining the formats and behaviors of the color targets
   * this pipeline writes to.
   */
  targets: Iterable<
    | GPUColorTargetState
    | null
    | undefined
  >;
}

interface GPUMultisampleState {
  /**
   * Number of samples per pixel. This {@link GPURenderPipeline} will be compatible only
   * with attachment textures ({@link GPURenderPassDescriptor#colorAttachments}
   * and {@link GPURenderPassDescriptor#depthStencilAttachment})
   * with matching {@link GPUTextureDescriptor#sampleCount}s.
   */
  count?: GPUSize32;
  /**
   * Mask determining which samples are written to.
   */
  mask?: GPUSampleMask;
  /**
   * When `true` indicates that a fragment's alpha channel should be used to generate a sample
   * coverage mask.
   */
  alphaToCoverageEnabled?: boolean;
}

interface GPUObjectDescriptorBase {
  /**
   * The initial value of {@link GPUObjectBase#label | GPUObjectBase.label}.
   */
  label?: string;
}

interface GPUOrigin2DDict {
  x?: GPUIntegerCoordinate;
  y?: GPUIntegerCoordinate;
}

interface GPUOrigin3DDict {
  x?: GPUIntegerCoordinate;
  y?: GPUIntegerCoordinate;
  z?: GPUIntegerCoordinate;
}

interface GPUPipelineDescriptorBase
  extends GPUObjectDescriptorBase {
  /**
   * The {@link GPUPipelineLayout} for this pipeline, or {@link GPUAutoLayoutMode} `"auto"` to generate
   * the pipeline layout automatically.
   * Note: If {@link GPUAutoLayoutMode} `"auto"` is used the pipeline cannot share {@link GPUBindGroup}s
   * with any other pipelines.
   */
  layout:
    | GPUPipelineLayout
    | GPUAutoLayoutMode;
}

interface GPUPipelineErrorInit {
  reason: GPUPipelineErrorReason;
}

interface GPUPipelineLayoutDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * A list of optional {@link GPUBindGroupLayout}s the pipeline will use. Each element corresponds
   * to a @group attribute in the {@link GPUShaderModule}, with the `N`th element corresponding
   * with `@group(N)`.
   */
  bindGroupLayouts: Iterable<
    | GPUBindGroupLayout
    | null
    | undefined
  >;
}

interface GPUPrimitiveState {
  /**
   * The type of primitive to be constructed from the vertex inputs.
   */
  topology?: GPUPrimitiveTopology;
  /**
   * For pipelines with strip topologies
   * ({@link GPUPrimitiveTopology} `"line-strip"` or {@link GPUPrimitiveTopology} `"triangle-strip"`),
   * this determines the index buffer format and primitive restart value
   * ({@link GPUIndexFormat} `"uint16"`/`0xFFFF` or {@link GPUIndexFormat} `"uint32"`/`0xFFFFFFFF`).
   * It is not allowed on pipelines with non-strip topologies.
   * Note: Some implementations require knowledge of the primitive restart value to compile
   * pipeline state objects.
   * To use a strip-topology pipeline with an indexed draw call
   * ({@link GPURenderCommandsMixin#drawIndexed()} or {@link GPURenderCommandsMixin#drawIndexedIndirect}),
   * this must be set, and it must match the index buffer format used with the draw call
   * (set in {@link GPURenderCommandsMixin#setIndexBuffer}).
   * See {@link https://www.w3.org/TR/webgpu/#primitive-assembly} for additional details.
   */
  stripIndexFormat?: GPUIndexFormat;
  /**
   * Defines which polygons are considered front-facing.
   */
  frontFace?: GPUFrontFace;
  /**
   * Defines which polygon orientation will be culled, if any.
   */
  cullMode?: GPUCullMode;
  /**
   * If true, indicates that depth clipping is disabled.
   * Requires the {@link GPUFeatureName} `"depth-clip-control"` feature to be enabled.
   */
  unclippedDepth?: boolean;
}

interface GPUProgrammableStage {
  /**
   * The {@link GPUShaderModule} containing the code that this programmable stage will execute.
   */
  module: GPUShaderModule;
  /**
   * The name of the function in {@link GPUProgrammableStage#module} that this stage will use to
   * perform its work.
   * NOTE: Since the {@link GPUProgrammableStage#entryPoint} dictionary member is
   * not required, methods which consume a {@link GPUProgrammableStage} must use the
   * "[$get the entry point$]" algorithm to determine which entry point
   * it refers to.
   */
  entryPoint?: string;
  /**
   * Specifies the values of pipeline-overridable constants in the shader module
   * {@link GPUProgrammableStage#module}.
   * Each such pipeline-overridable constant is uniquely identified by a single
   * pipeline-overridable constant identifier string, representing the pipeline
   * constant ID of the constant if its declaration specifies one, and otherwise the
   * constant's identifier name.
   * The key of each key-value pair must equal the
   * pipeline-overridable constant identifier string|identifier string
   * of one such constant, with the comparison performed
   * according to the rules for WGSL identifier comparison.
   * When the pipeline is executed, that constant will have the specified value.
   * Values are specified as <dfn typedef for="">GPUPipelineConstantValue</dfn>, which is a `double`.
   * They are converted [$to WGSL type$] of the pipeline-overridable constant (`bool`/`i32`/`u32`/`f32`/`f16`).
   * If conversion fails, a validation error is generated.
   * <div class=example>
   * Pipeline-overridable constants defined in WGSL:
   * ```wgsl
   * @id(0)      override has_point_light: bool = true;  // Algorithmic control.
   * @id(1200)   override specular_param: f32 = 2.3;     // Numeric control.
   * @id(1300)   override gain: f32;                     // Must be overridden.
   * override width: f32 = 0.0;              // Specifed at the API level
   * //   using the name "width".
   * override depth: f32;                    // Specifed at the API level
   * //   using the name "depth".
   * //   Must be overridden.
   * override height = 2 * depth;            // The default value
   * // (if not set at the API level),
   * // depends on another
   * // overridable constant.
   * ```
   * Corresponding JavaScript code, providing only the overrides which are required
   * (have no defaults):
   * ```js
   * {
   * // ...
   * constants: {
   * 1300: 2.0,  // "gain"
   * depth: -1,  // "depth"
   * }
   * }
   * ```
   * Corresponding JavaScript code, overriding all constants:
   * ```js
   * {
   * // ...
   * constants: {
   * 0: false,   // "has_point_light"
   * 1200: 3.0,  // "specular_param"
   * 1300: 2.0,  // "gain"
   * width: 20,  // "width"
   * depth: -1,  // "depth"
   * height: 15, // "height"
   * }
   * }
   * ```
   * </div>
   */
  constants?: Record<
    string,
    GPUPipelineConstantValue
  >;
}

interface GPUQuerySetDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The type of queries managed by {@link GPUQuerySet}.
   */
  type: GPUQueryType;
  /**
   * The number of queries managed by {@link GPUQuerySet}.
   */
  count: GPUSize32;
}

type GPUQueueDescriptor =
  GPUObjectDescriptorBase;
type GPURenderBundleDescriptor =
  GPUObjectDescriptorBase;

interface GPURenderBundleEncoderDescriptor
  extends GPURenderPassLayout {
  /**
   * If `true`, indicates that the render bundle does not modify the depth component of the
   * {@link GPURenderPassDepthStencilAttachment} of any render pass the render bundle is executed
   * in.
   * See read-only depth-stencil.
   */
  depthReadOnly?: boolean;
  /**
   * If `true`, indicates that the render bundle does not modify the stencil component of the
   * {@link GPURenderPassDepthStencilAttachment} of any render pass the render bundle is executed
   * in.
   * See read-only depth-stencil.
   */
  stencilReadOnly?: boolean;
}

interface GPURenderPassColorAttachment {
  /**
   * A {@link GPUTextureView} describing the texture subresource that will be output to for this
   * color attachment.
   */
  view: GPUTextureView;
  /**
   * Indicates the depth slice index of {@link GPUTextureViewDimension} `"3d"` {@link GPURenderPassColorAttachment#view}
   * that will be output to for this color attachment.
   */
  depthSlice?: GPUIntegerCoordinate;
  /**
   * A {@link GPUTextureView} describing the texture subresource that will receive the resolved
   * output for this color attachment if {@link GPURenderPassColorAttachment#view} is
   * multisampled.
   */
  resolveTarget?: GPUTextureView;
  /**
   * Indicates the value to clear {@link GPURenderPassColorAttachment#view} to prior to executing the
   * render pass. If not map/exist|provided, defaults to `{r: 0, g: 0, b: 0, a: 0}`. Ignored
   * if {@link GPURenderPassColorAttachment#loadOp} is not {@link GPULoadOp} `"clear"`.
   * The components of {@link GPURenderPassColorAttachment#clearValue} are all double values.
   * They are converted [$to a texel value of texture format$] matching the render attachment.
   * If conversion fails, a validation error is generated.
   */
  clearValue?: GPUColor;
  /**
   * Indicates the load operation to perform on {@link GPURenderPassColorAttachment#view} prior to
   * executing the render pass.
   * Note: It is recommended to prefer clearing; see {@link GPULoadOp} `"clear"` for details.
   */
  loadOp: GPULoadOp;
  /**
   * The store operation to perform on {@link GPURenderPassColorAttachment#view}
   * after executing the render pass.
   */
  storeOp: GPUStoreOp;
}

interface GPURenderPassDepthStencilAttachment {
  /**
   * A {@link GPUTextureView} describing the texture subresource that will be output to
   * and read from for this depth/stencil attachment.
   */
  view: GPUTextureView;
  /**
   * Indicates the value to clear {@link GPURenderPassDepthStencilAttachment#view}'s depth component
   * to prior to executing the render pass. Ignored if {@link GPURenderPassDepthStencilAttachment#depthLoadOp}
   * is not {@link GPULoadOp} `"clear"`. Must be between 0.0 and 1.0, inclusive.
   * <!-- POSTV1(unrestricted-depth): unless unrestricted depth is enabled -->
   */
  depthClearValue?: number;
  /**
   * Indicates the load operation to perform on {@link GPURenderPassDepthStencilAttachment#view}'s
   * depth component prior to executing the render pass.
   * Note: It is recommended to prefer clearing; see {@link GPULoadOp} `"clear"` for details.
   */
  depthLoadOp?: GPULoadOp;
  /**
   * The store operation to perform on {@link GPURenderPassDepthStencilAttachment#view}'s
   * depth component after executing the render pass.
   */
  depthStoreOp?: GPUStoreOp;
  /**
   * Indicates that the depth component of {@link GPURenderPassDepthStencilAttachment#view}
   * is read only.
   */
  depthReadOnly?: boolean;
  /**
   * Indicates the value to clear {@link GPURenderPassDepthStencilAttachment#view}'s stencil component
   * to prior to executing the render pass. Ignored if {@link GPURenderPassDepthStencilAttachment#stencilLoadOp}
   * is not {@link GPULoadOp} `"clear"`.
   * The value will be converted to the type of the stencil aspect of `view` by taking the same
   * number of LSBs as the number of bits in the stencil aspect of one texel block|texel of `view`.
   */
  stencilClearValue?: GPUStencilValue;
  /**
   * Indicates the load operation to perform on {@link GPURenderPassDepthStencilAttachment#view}'s
   * stencil component prior to executing the render pass.
   * Note: It is recommended to prefer clearing; see {@link GPULoadOp} `"clear"` for details.
   */
  stencilLoadOp?: GPULoadOp;
  /**
   * The store operation to perform on {@link GPURenderPassDepthStencilAttachment#view}'s
   * stencil component after executing the render pass.
   */
  stencilStoreOp?: GPUStoreOp;
  /**
   * Indicates that the stencil component of {@link GPURenderPassDepthStencilAttachment#view}
   * is read only.
   */
  stencilReadOnly?: boolean;
}

interface GPURenderPassDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The set of {@link GPURenderPassColorAttachment} values in this sequence defines which
   * color attachments will be output to when executing this render pass.
   * Due to compatible usage list|usage compatibility, no color attachment
   * may alias another attachment or any resource used inside the render pass.
   */
  colorAttachments: Iterable<
    | GPURenderPassColorAttachment
    | null
    | undefined
  >;
  /**
   * The {@link GPURenderPassDepthStencilAttachment} value that defines the depth/stencil
   * attachment that will be output to and tested against when executing this render pass.
   * Due to compatible usage list|usage compatibility, no writable depth/stencil attachment
   * may alias another attachment or any resource used inside the render pass.
   */
  depthStencilAttachment?: GPURenderPassDepthStencilAttachment;
  /**
   * The {@link GPUQuerySet} value defines where the occlusion query results will be stored for this pass.
   */
  occlusionQuerySet?: GPUQuerySet;
  /**
   * Defines which timestamp values will be written for this pass, and where to write them to.
   */
  timestampWrites?: GPURenderPassTimestampWrites;
  /**
   * The maximum number of draw calls that will be done in the render pass. Used by some
   * implementations to size work injected before the render pass. Keeping the default value
   * is a good default, unless it is known that more draw calls will be done.
   */
  maxDrawCount?: GPUSize64;
}

interface GPURenderPassLayout
  extends GPUObjectDescriptorBase {
  /**
   * A list of the {@link GPUTextureFormat}s of the color attachments for this pass or bundle.
   */
  colorFormats: Iterable<
    | GPUTextureFormat
    | null
    | undefined
  >;
  /**
   * The {@link GPUTextureFormat} of the depth/stencil attachment for this pass or bundle.
   */
  depthStencilFormat?: GPUTextureFormat;
  /**
   * Number of samples per pixel in the attachments for this pass or bundle.
   */
  sampleCount?: GPUSize32;
}

interface GPURenderPassTimestampWrites {
  /**
   * The {@link GPUQuerySet}, of type {@link GPUQueryType} `"timestamp"`, that the query results will be
   * written to.
   */
  querySet: GPUQuerySet;
  /**
   * If defined, indicates the query index in {@link GPURenderPassTimestampWrites#querySet} into
   * which the timestamp at the beginning of the render pass will be written.
   */
  beginningOfPassWriteIndex?: GPUSize32;
  /**
   * If defined, indicates the query index in {@link GPURenderPassTimestampWrites#querySet} into
   * which the timestamp at the end of the render pass will be written.
   */
  endOfPassWriteIndex?: GPUSize32;
}

interface GPURenderPipelineDescriptor
  extends GPUPipelineDescriptorBase {
  /**
   * Describes the vertex shader entry point of the pipeline and its input buffer layouts.
   */
  vertex: GPUVertexState;
  /**
   * Describes the primitive-related properties of the pipeline.
   */
  primitive?: GPUPrimitiveState;
  /**
   * Describes the optional depth-stencil properties, including the testing, operations, and bias.
   */
  depthStencil?: GPUDepthStencilState;
  /**
   * Describes the multi-sampling properties of the pipeline.
   */
  multisample?: GPUMultisampleState;
  /**
   * Describes the fragment shader entry point of the pipeline and its output colors. If
   * not map/exist|provided, the {@link https://www.w3.org/TR/webgpu/#no-color-output} mode is enabled.
   */
  fragment?: GPUFragmentState;
}

interface GPURequestAdapterOptions {
  /**
   * "Feature level" for the adapter request.
   * The allowed <dfn dfn for="">feature level string</dfn> values are:
   * <dl dfn-type=dfn dfn-for="feature level string">
   * : <dfn noexport>"core"</dfn>
   * No effect.
   * : <dfn noexport>"compatibility"</dfn>
   * No effect.
   * Note:
   * This value is reserved for future use as a way to opt into additional validation restrictions.
   * Applications should not use this value at this time.
   */
  featureLevel?: string;
  /**
   * Optionally provides a hint indicating what class of adapter should be selected from
   * the system's available adapters.
   * The value of this hint may influence which adapter is chosen, but it must not
   * influence whether an adapter is returned or not.
   * Note:
   * The primary utility of this hint is to influence which GPU is used in a multi-GPU system.
   * For instance, some laptops have a low-power integrated GPU and a high-performance
   * discrete GPU. This hint may also affect the power configuration of the selected GPU to
   * match the requested power preference.
   * Note:
   * Depending on the exact hardware configuration, such as battery status and attached displays
   * or removable GPUs, the user agent may select different adapters given the same power
   * preference.
   * Typically, given the same hardware configuration and state and
   * `powerPreference`, the user agent is likely to select the same adapter.
   */
  powerPreference?: GPUPowerPreference;
  /**
   * When set to `true` indicates that only a fallback adapter may be returned. If the user
   * agent does not support a fallback adapter, will cause {@link GPU#requestAdapter} to
   * resolve to `null`.
   * Note:
   * {@link GPU#requestAdapter} may still return a fallback adapter if
   * {@link GPURequestAdapterOptions#forceFallbackAdapter} is set to `false` and either no
   * other appropriate adapter is available or the user agent chooses to return a
   * fallback adapter. Developers that wish to prevent their applications from running on
   * fallback adapters should check the {@link GPUAdapter#info}.{@link GPUAdapterInfo#isFallbackAdapter}
   * attribute prior to requesting a {@link GPUDevice}.
   */
  forceFallbackAdapter?: boolean;
  xrCompatible?: boolean;
}

interface GPUSamplerBindingLayout {
  /**
   * Indicates the required type of a sampler bound to this bindings.
   */
  type?: GPUSamplerBindingType;
}

interface GPUSamplerDescriptor
  extends GPUObjectDescriptorBase {
  /**
   */
  addressModeU?: GPUAddressMode;
  /**
   */
  addressModeV?: GPUAddressMode;
  /**
   * Specifies the {@link GPUAddressMode | address modes} for the texture width, height, and depth
   * coordinates, respectively.
   */
  addressModeW?: GPUAddressMode;
  /**
   * Specifies the sampling behavior when the sampled area is smaller than or equal to one
   * texel.
   */
  magFilter?: GPUFilterMode;
  /**
   * Specifies the sampling behavior when the sampled area is larger than one texel.
   */
  minFilter?: GPUFilterMode;
  /**
   * Specifies behavior for sampling between mipmap levels.
   */
  mipmapFilter?: GPUMipmapFilterMode;
  /**
   */
  lodMinClamp?: number;
  /**
   * Specifies the minimum and maximum levels of detail, respectively, used internally when
   * sampling a texture.
   */
  lodMaxClamp?: number;
  /**
   * When provided the sampler will be a comparison sampler with the specified
   * {@link GPUCompareFunction}.
   * Note: Comparison samplers may use filtering, but the sampling results will be
   * implementation-dependent and may differ from the normal filtering rules.
   */
  compare?: GPUCompareFunction;
  /**
   * Specifies the maximum anisotropy value clamp used by the sampler. Anisotropic filtering is
   * enabled when {@link GPUSamplerDescriptor#maxAnisotropy} is &gt; 1 and the implementation supports it.
   * Anisotropic filtering improves the image quality of textures sampled at oblique viewing
   * angles. Higher {@link GPUSamplerDescriptor#maxAnisotropy} values indicate the maximum ratio of
   * anisotropy supported when filtering.
   * <div class=note heading>
   * Most implementations support {@link GPUSamplerDescriptor#maxAnisotropy} values in range
   * between 1 and 16, inclusive. The used value of {@link GPUSamplerDescriptor#maxAnisotropy}
   * will be clamped to the maximum value that the platform supports.
   * The precise filtering behavior is implementation-dependent.
   * </div>
   */
  maxAnisotropy?: number;
}

interface GPUShaderModuleCompilationHint {
  entryPoint: string;
  /**
   * A {@link GPUPipelineLayout} that the {@link GPUShaderModule} may be used with in a future
   * {@link GPUDevice#createComputePipeline()} or {@link GPUDevice#createRenderPipeline} call.
   * If set to {@link GPUAutoLayoutMode} `"auto"` the layout will be the [$default pipeline layout$]
   * for the entry point associated with this hint will be used.
   */
  layout?:
    | GPUPipelineLayout
    | GPUAutoLayoutMode;
}

interface GPUShaderModuleDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The <a href="https://gpuweb.github.io/gpuweb/wgsl/">WGSL</a> source code for the shader
   * module.
   */
  code: string;
  /**
   * A list of {@link GPUShaderModuleCompilationHint}s.
   * Any hint provided by an application **should** contain information about one entry point of
   * a pipeline that will eventually be created from the entry point.
   * Implementations **should** use any information present in the {@link GPUShaderModuleCompilationHint}
   * to perform as much compilation as is possible within {@link GPUDevice#createShaderModule}.
   * Aside from type-checking, these hints are not validated in any way.
   * <div class=note heading>
   * Supplying information in {@link GPUShaderModuleDescriptor#compilationHints} does not have any
   * observable effect, other than performance. It may be detrimental to performance to
   * provide hints for pipelines that never end up being created.
   * Because a single shader module can hold multiple entry points, and multiple pipelines
   * can be created from a single shader module, it can be more performant for an
   * implementation to do as much compilation as possible once in
   * {@link GPUDevice#createShaderModule} rather than multiple times in the multiple calls to
   * {@link GPUDevice#createComputePipeline()} or {@link GPUDevice#createRenderPipeline}.
   * Hints are only applied to the entry points they explicitly name.
   * Unlike {@link GPUProgrammableStage#entryPoint | GPUProgrammableStage.entryPoint},
   * there is no default, even if only one entry point is present in the module.
   * </div>
   * Note:
   * Hints are not validated in an observable way, but user agents **may** surface identifiable
   * errors (like unknown entry point names or incompatible pipeline layouts) to developers,
   * for example in the browser developer console.
   */
  compilationHints?: Iterable<GPUShaderModuleCompilationHint>;
}

interface GPUStencilFaceState {
  /**
   * The {@link GPUCompareFunction} used when testing the RenderState.`[[stencilReference]]` value
   * against the fragment's {@link GPURenderPassDescriptor#depthStencilAttachment} stencil values.
   */
  compare?: GPUCompareFunction;
  /**
   * The {@link GPUStencilOperation} performed if the fragment stencil comparison test described by
   * {@link GPUStencilFaceState#compare} fails.
   */
  failOp?: GPUStencilOperation;
  /**
   * The {@link GPUStencilOperation} performed if the fragment depth comparison described by
   * {@link GPUDepthStencilState#depthCompare} fails.
   */
  depthFailOp?: GPUStencilOperation;
  /**
   * The {@link GPUStencilOperation} performed if the fragment stencil comparison test described by
   * {@link GPUStencilFaceState#compare} passes.
   */
  passOp?: GPUStencilOperation;
}

interface GPUStorageTextureBindingLayout {
  /**
   * The access mode for this binding, indicating readability and writability.
   */
  access?: GPUStorageTextureAccess;
  /**
   * The required {@link GPUTextureViewDescriptor#format} of texture views bound to this binding.
   */
  format: GPUTextureFormat;
  /**
   * Indicates the required {@link GPUTextureViewDescriptor#dimension} for texture views bound to
   * this binding.
   */
  viewDimension?: GPUTextureViewDimension;
}

interface GPUTexelCopyBufferInfo
  extends GPUTexelCopyBufferLayout {
  /**
   * A buffer which either contains texel data to be copied or will store the texel data being
   * copied, depending on the method it is being passed to.
   */
  buffer: GPUBuffer;
}

interface GPUTexelCopyBufferLayout {
  /**
   * The offset, in bytes, from the beginning of the texel data source (such as a
   * {@link GPUTexelCopyBufferInfo#buffer | GPUTexelCopyBufferInfo.buffer}) to the start of the texel data
   * within that source.
   */
  offset?: GPUSize64;
  /**
   * The stride, in bytes, between the beginning of each texel block row and the subsequent
   * texel block row.
   * Required if there are multiple texel block rows (i.e. the copy height or depth is more
   * than one block).
   */
  bytesPerRow?: GPUSize32;
  /**
   * Number of texel block rows per single texel image of the texture.
   * {@link GPUTexelCopyBufferLayout#rowsPerImage} &times;
   * {@link GPUTexelCopyBufferLayout#bytesPerRow} is the stride, in bytes, between the beginning of each
   * texel image of data and the subsequent texel image.
   * Required if there are multiple texel images (i.e. the copy depth is more than one).
   */
  rowsPerImage?: GPUSize32;
}

interface GPUTexelCopyTextureInfo {
  /**
   * Texture to copy to/from.
   */
  texture: GPUTexture;
  /**
   * Mip-map level of the {@link GPUTexelCopyTextureInfo#texture} to copy to/from.
   */
  mipLevel?: GPUIntegerCoordinate;
  /**
   * Defines the origin of the copy - the minimum corner of the texture sub-region to copy to/from.
   * Together with `copySize`, defines the full copy sub-region.
   */
  origin?: GPUOrigin3D;
  /**
   * Defines which aspects of the {@link GPUTexelCopyTextureInfo#texture} to copy to/from.
   */
  aspect?: GPUTextureAspect;
}

interface GPUTextureBindingLayout {
  /**
   * Indicates the type required for texture views bound to this binding.
   */
  sampleType?: GPUTextureSampleType;
  /**
   * Indicates the required {@link GPUTextureViewDescriptor#dimension} for texture views bound to
   * this binding.
   */
  viewDimension?: GPUTextureViewDimension;
  /**
   * Indicates whether or not texture views bound to this binding must be multisampled.
   */
  multisampled?: boolean;
}

interface GPUTextureDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The width, height, and depth or layer count of the texture.
   */
  size: GPUExtent3DStrict;
  /**
   * The number of mip levels the texture will contain.
   */
  mipLevelCount?: GPUIntegerCoordinate;
  /**
   * The sample count of the texture. A {@link GPUTextureDescriptor#sampleCount} &gt; `1` indicates
   * a multisampled texture.
   */
  sampleCount?: GPUSize32;
  /**
   * Whether the texture is one-dimensional, an array of two-dimensional layers, or three-dimensional.
   */
  dimension?: GPUTextureDimension;
  /**
   * The format of the texture.
   */
  format: GPUTextureFormat;
  /**
   * The allowed usages for the texture.
   */
  usage: GPUTextureUsageFlags;
  /**
   * Specifies what view {@link GPUTextureViewDescriptor#format} values will be allowed when calling
   * {@link GPUTexture#createView} on this texture (in addition to the texture's actual
   * {@link GPUTextureDescriptor#format}).
   * <div class=note heading>
   * Adding a format to this list may have a significant performance impact, so it is best
   * to avoid adding formats unnecessarily.
   * The actual performance impact is highly dependent on the target system; developers must
   * test various systems to find out the impact on their particular application.
   * For example, on some systems any texture with a {@link GPUTextureDescriptor#format} or
   * {@link GPUTextureDescriptor#viewFormats} entry including
   * {@link GPUTextureFormat} `"rgba8unorm-srgb"` will perform less optimally than a
   * {@link GPUTextureFormat} `"rgba8unorm"` texture which does not.
   * Similar caveats exist for other formats and pairs of formats on other systems.
   * </div>
   * Formats in this list must be texture view format compatible with the texture format.
   * <div algorithm data-timeline=const>
   * Two {@link GPUTextureFormat}s `format` and `viewFormat` are <dfn dfn for="">texture view format compatible</dfn> if:
   * - `format` equals `viewFormat`, or
   * - `format` and `viewFormat` differ only in whether they are `srgb` formats (have the `-srgb` suffix).
   * </div>
   */
  viewFormats?: Iterable<GPUTextureFormat>;
  /**
   * **PROPOSED** in [Compatibility Mode](https://github.com/gpuweb/gpuweb/blob/main/proposals/compatibility-mode.md).
   *
   * > [In compatibility mode,]
   * > When specifying a texture, a textureBindingViewDimension property
   * > determines the views which can be bound from that texture for sampling.
   * > Binding a view of a different dimension for sampling than specified at
   * > texture creation time will cause a validation error.
   */
  textureBindingViewDimension?: GPUTextureViewDimension;
}

interface GPUTextureViewDescriptor
  extends GPUObjectDescriptorBase {
  /**
   * The format of the texture view. Must be either the {@link GPUTextureDescriptor#format} of the
   * texture or one of the {@link GPUTextureDescriptor#viewFormats} specified during its creation.
   */
  format?: GPUTextureFormat;
  /**
   * The dimension to view the texture as.
   */
  dimension?: GPUTextureViewDimension;
  /**
   * The allowed {@link GPUTextureUsage | usage(s)} for the texture view. Must be a subset of the
   * {@link GPUTexture#usage} flags of the texture. If 0, defaults to the full set of
   * {@link GPUTexture#usage} flags of the texture.
   * Note: If the view's {@link GPUTextureViewDescriptor#format} doesn't support all of the
   * texture's {@link GPUTextureDescriptor#usage}s, the default will fail,
   * and the view's {@link GPUTextureViewDescriptor#usage} must be specified explicitly.
   */
  usage?: GPUTextureUsageFlags;
  /**
   * Which {@link GPUTextureAspect | aspect(s)} of the texture are accessible to the texture view.
   */
  aspect?: GPUTextureAspect;
  /**
   * The first (most detailed) mipmap level accessible to the texture view.
   */
  baseMipLevel?: GPUIntegerCoordinate;
  /**
   * How many mipmap levels, starting with {@link GPUTextureViewDescriptor#baseMipLevel}, are accessible to
   * the texture view.
   */
  mipLevelCount?: GPUIntegerCoordinate;
  /**
   * The index of the first array layer accessible to the texture view.
   */
  baseArrayLayer?: GPUIntegerCoordinate;
  /**
   * How many array layers, starting with {@link GPUTextureViewDescriptor#baseArrayLayer}, are accessible
   * to the texture view.
   */
  arrayLayerCount?: GPUIntegerCoordinate;
}

interface GPUUncapturedErrorEventInit
  extends EventInit {
  error: GPUError;
}

interface GPUVertexAttribute {
  /**
   * The {@link GPUVertexFormat} of the attribute.
   */
  format: GPUVertexFormat;
  /**
   * The offset, in bytes, from the beginning of the element to the data for the attribute.
   */
  offset: GPUSize64;
  /**
   * The numeric location associated with this attribute, which will correspond with a
   * <a href="https://gpuweb.github.io/gpuweb/wgsl/#input-output-locations">"@location" attribute</a>
   * declared in the {@link GPURenderPipelineDescriptor#vertex}.{@link GPUProgrammableStage#module | module}.
   */
  shaderLocation: GPUIndex32;
}

interface GPUVertexBufferLayout {
  /**
   * The stride, in bytes, between elements of this array.
   */
  arrayStride: GPUSize64;
  /**
   * Whether each element of this array represents per-vertex data or per-instance data
   */
  stepMode?: GPUVertexStepMode;
  /**
   * An array defining the layout of the vertex attributes within each element.
   */
  attributes: Iterable<GPUVertexAttribute>;
}

interface GPUVertexState
  extends GPUProgrammableStage {
  /**
   * A list of {@link GPUVertexBufferLayout}s, each defining the layout of vertex attribute data in a
   * vertex buffer used by this pipeline.
   */
  buffers?: Iterable<
    | GPUVertexBufferLayout
    | null
    | undefined
  >;
}

interface GPUBindingCommandsMixin {
  /**
   * Sets the current {@link GPUBindGroup} for the given index.
   * @param index - The index to set the bind group at.
   * @param bindGroup - Bind group to use for subsequent render or compute commands.
   * @param dynamicOffsets - Array containing buffer offsets in bytes for each entry in
   * 	`bindGroup` marked as {@link GPUBindGroupLayoutEntry#buffer}.{@link GPUBufferBindingLayout#hasDynamicOffset}.-->
   */
  setBindGroup(
    index: GPUIndex32,
    bindGroup:
      | GPUBindGroup
      | null
      | undefined,
    dynamicOffsets?: Iterable<GPUBufferDynamicOffset>
  ): undefined;
  /**
   * Sets the current {@link GPUBindGroup} for the given index, specifying dynamic offsets as a subset
   * of a Uint32Array.
   * @param index - The index to set the bind group at.
   * @param bindGroup - Bind group to use for subsequent render or compute commands.
   * @param dynamicOffsetsData - Array containing buffer offsets in bytes for each entry in
   * 	`bindGroup` marked as {@link GPUBindGroupLayoutEntry#buffer}.{@link GPUBufferBindingLayout#hasDynamicOffset}.
   * @param dynamicOffsetsDataStart - Offset in elements into `dynamicOffsetsData` where the
   * 	buffer offset data begins.
   * @param dynamicOffsetsDataLength - Number of buffer offsets to read from `dynamicOffsetsData`.
   */
  setBindGroup(
    index: GPUIndex32,
    bindGroup:
      | GPUBindGroup
      | null
      | undefined,
    dynamicOffsetsData: Uint32Array,
    dynamicOffsetsDataStart: GPUSize64,
    dynamicOffsetsDataLength: GPUSize32
  ): undefined;
}

interface GPUCommandsMixin {}

interface GPUDebugCommandsMixin {
  /**
   * Begins a labeled debug group containing subsequent commands.
   * @param groupLabel - The label for the command group.
   */
  pushDebugGroup(
    groupLabel: string
  ): undefined;
  /**
   * Ends the labeled debug group most recently started by {@link GPUDebugCommandsMixin#pushDebugGroup}.
   */
  popDebugGroup(): undefined;
  /**
   * Marks a point in a stream of commands with a label.
   * @param markerLabel - The label to insert.
   */
  insertDebugMarker(
    markerLabel: string
  ): undefined;
}

interface GPUObjectBase {
  label: string;
}

interface GPUPipelineBase {
  /**
   * Gets a {@link GPUBindGroupLayout} that is compatible with the {@link GPUPipelineBase}'s
   * {@link GPUBindGroupLayout} at `index`.
   * @param index - Index into the pipeline layout's {@link GPUPipelineLayout}.`[[bindGroupLayouts]]`
   * 	sequence.
   */
  getBindGroupLayout(
    index: number
  ): GPUBindGroupLayout;
}

interface GPURenderCommandsMixin {
  /**
   * Sets the current {@link GPURenderPipeline}.
   * @param pipeline - The render pipeline to use for subsequent drawing commands.
   */
  setPipeline(
    pipeline: GPURenderPipeline
  ): undefined;
  /**
   * Sets the current index buffer.
   * @param buffer - Buffer containing index data to use for subsequent drawing commands.
   * @param indexFormat - Format of the index data contained in `buffer`.
   * @param offset - Offset in bytes into `buffer` where the index data begins. Defaults to `0`.
   * @param size - Size in bytes of the index data in `buffer`.
   * 	Defaults to the size of the buffer minus the offset.
   */
  setIndexBuffer(
    buffer: GPUBuffer,
    indexFormat: GPUIndexFormat,
    offset?: GPUSize64,
    size?: GPUSize64
  ): undefined;
  /**
   * Sets the current vertex buffer for the given slot.
   * @param slot - The vertex buffer slot to set the vertex buffer for.
   * @param buffer - Buffer containing vertex data to use for subsequent drawing commands.
   * @param offset - Offset in bytes into `buffer` where the vertex data begins. Defaults to `0`.
   * @param size - Size in bytes of the vertex data in `buffer`.
   * 	Defaults to the size of the buffer minus the offset.
   */
  setVertexBuffer(
    slot: GPUIndex32,
    buffer:
      | GPUBuffer
      | null
      | undefined,
    offset?: GPUSize64,
    size?: GPUSize64
  ): undefined;
  /**
   * Draws primitives.
   * See {@link https://www.w3.org/TR/webgpu/#rendering-operations} for the detailed specification.
   * @param vertexCount - The number of vertices to draw.
   * @param instanceCount - The number of instances to draw.
   * @param firstVertex - Offset into the vertex buffers, in vertices, to begin drawing from.
   * @param firstInstance - First instance to draw.
   */
  draw(
    vertexCount: GPUSize32,
    instanceCount?: GPUSize32,
    firstVertex?: GPUSize32,
    firstInstance?: GPUSize32
  ): undefined;
  /**
   * Draws indexed primitives.
   * See {@link https://www.w3.org/TR/webgpu/#rendering-operations} for the detailed specification.
   * @param indexCount - The number of indices to draw.
   * @param instanceCount - The number of instances to draw.
   * @param firstIndex - Offset into the index buffer, in indices, begin drawing from.
   * @param baseVertex - Added to each index value before indexing into the vertex buffers.
   * @param firstInstance - First instance to draw.
   */
  drawIndexed(
    indexCount: GPUSize32,
    instanceCount?: GPUSize32,
    firstIndex?: GPUSize32,
    baseVertex?: GPUSignedOffset32,
    firstInstance?: GPUSize32
  ): undefined;
  /**
   * Draws primitives using parameters read from a {@link GPUBuffer}.
   * See {@link https://www.w3.org/TR/webgpu/#rendering-operations} for the detailed specification.
   * packed block of **four 32-bit unsigned integer values (16 bytes total)**, given in the same
   * order as the arguments for {@link GPURenderCommandsMixin#draw}. For example:
   * @param indirectBuffer - Buffer containing the indirect draw parameters.
   * @param indirectOffset - Offset in bytes into `indirectBuffer` where the drawing data begins.
   */
  drawIndirect(
    indirectBuffer: GPUBuffer,
    indirectOffset: GPUSize64
  ): undefined;
  /**
   * Draws indexed primitives using parameters read from a {@link GPUBuffer}.
   * See {@link https://www.w3.org/TR/webgpu/#rendering-operations} for the detailed specification.
   * tightly packed block of **five 32-bit unsigned integer values (20 bytes total)**, given in
   * the same order as the arguments for {@link GPURenderCommandsMixin#drawIndexed}. For example:
   * @param indirectBuffer - Buffer containing the indirect drawIndexed parameters.
   * @param indirectOffset - Offset in bytes into `indirectBuffer` where the drawing data begins.
   */
  drawIndexedIndirect(
    indirectBuffer: GPUBuffer,
    indirectOffset: GPUSize64
  ): undefined;
}

interface NavigatorGPU {
  /**
   * A global singleton providing top-level entry points like {@link GPU#requestAdapter}.
   */
  readonly gpu: GPU;
}

interface GPU {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPU";
  /**
   * Requests an adapter from the user agent.
   * The user agent chooses whether to return an adapter, and, if so,
   * chooses according to the provided options.
   * @param options - Criteria used to select the adapter.
   */
  requestAdapter(
    options?: GPURequestAdapterOptions
  ): Promise<GPUAdapter | null>;
  /**
   * Returns an optimal {@link GPUTextureFormat} for displaying 8-bit depth, standard dynamic range
   * content on this system. Must only return {@link GPUTextureFormat} `"rgba8unorm"` or
   * {@link GPUTextureFormat} `"bgra8unorm"`.
   * The returned value can be passed as the {@link GPUCanvasConfiguration#format} to
   * {@link GPUCanvasContext#configure} calls on a {@link GPUCanvasContext} to ensure the associated
   * canvas is able to display its contents efficiently.
   * Note: Canvases which are not displayed to the screen may or may not benefit from using this
   * format.
   */
  getPreferredCanvasFormat(): GPUTextureFormat;
  readonly wgslLanguageFeatures: WGSLLanguageFeatures;
}

declare var GPU: {
  prototype: GPU;
  new (): never;
};

interface GPUAdapter {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUAdapter";
  /**
   * The set of values in `this`.{@link GPUAdapter#[[adapter]]}.{@link adapter#[[features]]}.
   */
  readonly features: GPUSupportedFeatures;
  /**
   * The limits in `this`.{@link GPUAdapter#[[adapter]]}.{@link adapter#[[limits]]}.
   */
  readonly limits: GPUSupportedLimits;
  /**
   * Information about the physical adapter underlying this {@link GPUAdapter}.
   * For a given {@link GPUAdapter}, the {@link GPUAdapterInfo} values exposed are constant over time.
   * The same object is returned each time. To create that object for the first time:
   * <div algorithm=GPUAdapter.info>
   * <div data-timeline=content>
   * **Called on:** {@link GPUAdapter} `this`.
   * **Returns:** {@link GPUAdapterInfo}
   * Content timeline steps:
   * 1. Return a [$new adapter info$] for `this.adapter`.
   * </div>
   * </div>
   */
  readonly info: GPUAdapterInfo;
  /**
   * Requests a device from the adapter.
   * This is a one-time action: if a device is returned successfully,
   * the adapter becomes {@link adapter#[[state]]#"consumed"}.
   * @param descriptor - Description of the {@link GPUDevice} to request.
   */
  requestDevice(
    descriptor?: GPUDeviceDescriptor
  ): Promise<GPUDevice>;
}

declare var GPUAdapter: {
  prototype: GPUAdapter;
  new (): never;
};

interface GPUAdapterInfo {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUAdapterInfo";
  /**
   * The name of the vendor of the adapter, if available. Empty string otherwise.
   */
  readonly vendor: string;
  /**
   * The name of the family or class of GPUs the adapter belongs to, if available. Empty
   * string otherwise.
   */
  readonly architecture: string;
  /**
   * A vendor-specific identifier for the adapter, if available. Empty string otherwise.
   * Note: This is a value that represents the type of adapter. For example, it may be a
   * [PCI device ID](https://pcisig.com/). It does not uniquely identify a given piece of
   * hardware like a serial number.
   */
  readonly device: string;
  /**
   * A human readable string describing the adapter as reported by the driver, if available.
   * Empty string otherwise.
   * Note: Because no formatting is applied to {@link GPUAdapterInfo#description} attempting to parse
   * this value is not recommended. Applications which change their behavior based on the
   * {@link GPUAdapterInfo}, such as applying workarounds for known driver issues, should rely on the
   * other fields when possible.
   */
  readonly description: string;
  /**
   * If the "subgroups" feature is supported, the minimum supported subgroup size for the
   * adapter.
   *
   * TODO: Temporarily optional until all browsers have implemented it.
   */
  readonly subgroupMinSize?: number;
  /**
   * If the "subgroups" feature is supported, the maximum supported subgroup size for the
   * adapter.
   *
   * TODO: Temporarily optional until all browsers have implemented it.
   */
  readonly subgroupMaxSize?: number;
  /**
   * Whether the adapter is a fallback adapter.
   **/
  readonly isFallbackAdapter: boolean;
}

declare var GPUAdapterInfo: {
  prototype: GPUAdapterInfo;
  new (): never;
};

interface GPUBindGroup
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUBindGroup";
}

declare var GPUBindGroup: {
  prototype: GPUBindGroup;
  new (): never;
};

interface GPUBindGroupLayout
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUBindGroupLayout";
}

declare var GPUBindGroupLayout: {
  prototype: GPUBindGroupLayout;
  new (): never;
};

interface GPUBuffer
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUBuffer";
  readonly size: GPUSize64Out;
  readonly usage: GPUFlagsConstant;
  readonly mapState: GPUBufferMapState;
  /**
   * Maps the given range of the {@link GPUBuffer} and resolves the returned Promise when the
   * {@link GPUBuffer}'s content is ready to be accessed with {@link GPUBuffer#getMappedRange}.
   * The resolution of the returned Promise **only** indicates that the buffer has been mapped.
   * It does not guarantee the completion of any other operations visible to the content timeline,
   * and in particular does not imply that any other Promise returned from
   * {@link GPUQueue#onSubmittedWorkDone()} or {@link GPUBuffer#mapAsync} on other {@link GPUBuffer}s
   * have resolved.
   * The resolution of the Promise returned from {@link GPUQueue#onSubmittedWorkDone}
   * **does** imply the completion of
   * {@link GPUBuffer#mapAsync} calls made prior to that call,
   * on {@link GPUBuffer}s last used exclusively on that queue.
   * @param mode - Whether the buffer should be mapped for reading or writing.
   * @param offset - Offset in bytes into the buffer to the start of the range to map.
   * @param size - Size in bytes of the range to map.
   */
  mapAsync(
    mode: GPUMapModeFlags,
    offset?: GPUSize64,
    size?: GPUSize64
  ): Promise<undefined>;
  /**
   * Returns an ArrayBuffer with the contents of the {@link GPUBuffer} in the given mapped range.
   * @param offset - Offset in bytes into the buffer to return buffer contents from.
   * @param size - Size in bytes of the ArrayBuffer to return.
   */
  getMappedRange(
    offset?: GPUSize64,
    size?: GPUSize64
  ): ArrayBuffer;
  /**
   * Unmaps the mapped range of the {@link GPUBuffer} and makes its contents available for use by the
   * GPU again.
   */
  unmap(): undefined;
  /**
   * Destroys the {@link GPUBuffer}.
   * Note: It is valid to destroy a buffer multiple times.
   * Note: Since no further operations can be enqueued using this buffer, implementations can
   * free resource allocations, including mapped memory that was just unmapped.
   */
  destroy(): undefined;
}

declare var GPUBuffer: {
  prototype: GPUBuffer;
  new (): never;
};

interface GPUCanvasContext {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUCanvasContext";
  /**
   * The canvas this context was created from.
   */
  readonly canvas:
    | HTMLCanvasElement
    | OffscreenCanvas;
  /**
   * Configures the context for this canvas.
   * This clears the drawing buffer to transparent black (in [$Replace the drawing buffer$]).
   * @param configuration - Desired configuration for the context.
   */
  configure(
    configuration: GPUCanvasConfiguration
  ): undefined;
  /**
   * Removes the context configuration. Destroys any textures produced while configured.
   */
  unconfigure(): undefined;
  /**
   * Returns the context configuration.
   */
  getConfiguration(): GPUCanvasConfigurationOut | null;
  /**
   * Get the {@link GPUTexture} that will be composited to the document by the {@link GPUCanvasContext}
   * next.
   * Note: The same {@link GPUTexture} object will be returned by every
   * call to {@link GPUCanvasContext#getCurrentTexture} until "[$Expire the current texture$]"
   * runs, even if that {@link GPUTexture} is destroyed, failed validation, or failed to allocate.
   */
  getCurrentTexture(): GPUTexture;
}

declare var GPUCanvasContext: {
  prototype: GPUCanvasContext;
  new (): never;
};

interface GPUCommandBuffer
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUCommandBuffer";
}

declare var GPUCommandBuffer: {
  prototype: GPUCommandBuffer;
  new (): never;
};

interface GPUCommandEncoder
  extends GPUObjectBase,
    GPUCommandsMixin,
    GPUDebugCommandsMixin {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUCommandEncoder";
  /**
   * Begins encoding a render pass described by `descriptor`.
   * @param descriptor - Description of the {@link GPURenderPassEncoder} to create.
   */
  beginRenderPass(
    descriptor: GPURenderPassDescriptor
  ): GPURenderPassEncoder;
  beginComputePass(
    descriptor?: GPUComputePassDescriptor
  ): GPUComputePassEncoder;
  /**
   * Shorthand, equivalent to {@link GPUCommandEncoder#copyBufferToBuffer}.
   */
  copyBufferToBuffer(
    source: GPUBuffer,
    destination: GPUBuffer,
    size?: GPUSize64
  ): undefined;
  /**
   * Encode a command into the {@link GPUCommandEncoder} that copies data from a sub-region of a
   * {@link GPUBuffer} to a sub-region of another {@link GPUBuffer}.
   * @param source - The {@link GPUBuffer} to copy from.
   * @param sourceOffset - Offset in bytes into `source` to begin copying from.
   * @param destination - The {@link GPUBuffer} to copy to.
   * @param destinationOffset - Offset in bytes into `destination` to place the copied data.
   * @param size - Bytes to copy. Defaults to the size of the buffer `source` minus `sourceOffset`.
   */
  copyBufferToBuffer(
    source: GPUBuffer,
    sourceOffset: GPUSize64,
    destination: GPUBuffer,
    destinationOffset: GPUSize64,
    size?: GPUSize64
  ): undefined;
  /**
   * Encode a command into the {@link GPUCommandEncoder} that copies data from a sub-region of a
   * {@link GPUBuffer} to a sub-region of one or multiple continuous texture subresources.
   * @param source - Combined with `copySize`, defines the region of the source buffer.
   * @param destination - Combined with `copySize`, defines the region of the destination texture subresource.
   * 	`copySize`:
   */
  copyBufferToTexture(
    source: GPUTexelCopyBufferInfo,
    destination: GPUTexelCopyTextureInfo,
    copySize: GPUExtent3DStrict
  ): undefined;
  /**
   * Encode a command into the {@link GPUCommandEncoder} that copies data from a sub-region of one or
   * multiple continuous texture subresources to a sub-region of a {@link GPUBuffer}.
   * @param source - Combined with `copySize`, defines the region of the source texture subresources.
   * @param destination - Combined with `copySize`, defines the region of the destination buffer.
   * 	`copySize`:
   */
  copyTextureToBuffer(
    source: GPUTexelCopyTextureInfo,
    destination: GPUTexelCopyBufferInfo,
    copySize: GPUExtent3DStrict
  ): undefined;
  /**
   * Encode a command into the {@link GPUCommandEncoder} that copies data from a sub-region of one
   * or multiple contiguous texture subresources to another sub-region of one or
   * multiple continuous texture subresources.
   * @param source - Combined with `copySize`, defines the region of the source texture subresources.
   * @param destination - Combined with `copySize`, defines the region of the destination texture subresources.
   * 	`copySize`:
   */
  copyTextureToTexture(
    source: GPUTexelCopyTextureInfo,
    destination: GPUTexelCopyTextureInfo,
    copySize: GPUExtent3DStrict
  ): undefined;
  /**
   * Encode a command into the {@link GPUCommandEncoder} that fills a sub-region of a
   * {@link GPUBuffer} with zeros.
   * @param buffer - The {@link GPUBuffer} to clear.
   * @param offset - Offset in bytes into `buffer` where the sub-region to clear begins.
   * @param size - Size in bytes of the sub-region to clear. Defaults to the size of the buffer minus `offset`.
   */
  clearBuffer(
    buffer: GPUBuffer,
    offset?: GPUSize64,
    size?: GPUSize64
  ): undefined;
  /**
   * Resolves query results from a {@link GPUQuerySet} out into a range of a {@link GPUBuffer}.
   * 	querySet:
   * 	firstQuery:
   * 	queryCount:
   * 	destination:
   * 	destinationOffset:
   */
  resolveQuerySet(
    querySet: GPUQuerySet,
    firstQuery: GPUSize32,
    queryCount: GPUSize32,
    destination: GPUBuffer,
    destinationOffset: GPUSize64
  ): undefined;
  /**
   * Completes recording of the commands sequence and returns a corresponding {@link GPUCommandBuffer}.
   * 	descriptor:
   */
  finish(
    descriptor?: GPUCommandBufferDescriptor
  ): GPUCommandBuffer;
}

declare var GPUCommandEncoder: {
  prototype: GPUCommandEncoder;
  new (): never;
};

interface GPUCompilationInfo {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUCompilationInfo";
  readonly messages: ReadonlyArray<GPUCompilationMessage>;
}

declare var GPUCompilationInfo: {
  prototype: GPUCompilationInfo;
  new (): never;
};

interface GPUCompilationMessage {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUCompilationMessage";
  /**
   * The human-readable, localizable text for this compilation message.
   * Note: The {@link GPUCompilationMessage#message} should follow the best practices for language
   * and direction information. This includes making use of any future standards which may
   * emerge regarding the reporting of string language and direction metadata.
   * <p class="note editorial"><span class=marker>Editorial note:</span>
   * At the time of this writing, no language/direction recommendation is available that provides
   * compatibility and consistency with legacy APIs, but when there is, adopt it formally.
   */
  readonly message: string;
  /**
   * The severity level of the message.
   * If the {@link GPUCompilationMessage#type} is {@link GPUCompilationMessageType} `"error"`, it
   * corresponds to a shader-creation error.
   */
  readonly type: GPUCompilationMessageType;
  /**
   * The line number in the shader {@link GPUShaderModuleDescriptor#code} the
   * {@link GPUCompilationMessage#message} corresponds to. Value is one-based, such that a lineNum of
   * `1` indicates the first line of the shader {@link GPUShaderModuleDescriptor#code}. Lines are
   * delimited by line breaks.
   * If the {@link GPUCompilationMessage#message} corresponds to a substring this points to
   * the line on which the substring begins. Must be `0` if the {@link GPUCompilationMessage#message}
   * does not correspond to any specific point in the shader {@link GPUShaderModuleDescriptor#code}.
   */
  readonly lineNum: number;
  /**
   * The offset, in UTF-16 code units, from the beginning of line {@link GPUCompilationMessage#lineNum}
   * of the shader {@link GPUShaderModuleDescriptor#code} to the point or beginning of the substring
   * that the {@link GPUCompilationMessage#message} corresponds to. Value is one-based, such that a
   * {@link GPUCompilationMessage#linePos} of `1` indicates the first code unit of the line.
   * If {@link GPUCompilationMessage#message} corresponds to a substring this points to the
   * first UTF-16 code unit of the substring. Must be `0` if the {@link GPUCompilationMessage#message}
   * does not correspond to any specific point in the shader {@link GPUShaderModuleDescriptor#code}.
   */
  readonly linePos: number;
  /**
   * The offset from the beginning of the shader {@link GPUShaderModuleDescriptor#code} in UTF-16
   * code units to the point or beginning of the substring that {@link GPUCompilationMessage#message}
   * corresponds to. Must reference the same position as {@link GPUCompilationMessage#lineNum} and
   * {@link GPUCompilationMessage#linePos}. Must be `0` if the {@link GPUCompilationMessage#message}
   * does not correspond to any specific point in the shader {@link GPUShaderModuleDescriptor#code}.
   */
  readonly offset: number;
  /**
   * The number of UTF-16 code units in the substring that {@link GPUCompilationMessage#message}
   * corresponds to. If the message does not correspond with a substring then
   * {@link GPUCompilationMessage#length} must be 0.
   */
  readonly length: number;
}

declare var GPUCompilationMessage: {
  prototype: GPUCompilationMessage;
  new (): never;
};

interface GPUComputePassEncoder
  extends GPUObjectBase,
    GPUCommandsMixin,
    GPUDebugCommandsMixin,
    GPUBindingCommandsMixin {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUComputePassEncoder";
  /**
   * Sets the current {@link GPUComputePipeline}.
   * @param pipeline - The compute pipeline to use for subsequent dispatch commands.
   */
  setPipeline(
    pipeline: GPUComputePipeline
  ): undefined;
  /**
   * Dispatch work to be performed with the current {@link GPUComputePipeline}.
   * See {@link https://www.w3.org/TR/webgpu/#computing-operations} for the detailed specification.
   * @param workgroupCountX - X dimension of the grid of workgroups to dispatch.
   * @param workgroupCountY - Y dimension of the grid of workgroups to dispatch.
   * @param workgroupCountZ - Z dimension of the grid of workgroups to dispatch.
   */
  dispatchWorkgroups(
    workgroupCountX: GPUSize32,
    workgroupCountY?: GPUSize32,
    workgroupCountZ?: GPUSize32
  ): undefined;
  /**
   * Dispatch work to be performed with the current {@link GPUComputePipeline} using parameters read
   * from a {@link GPUBuffer}.
   * See {@link https://www.w3.org/TR/webgpu/#computing-operations} for the detailed specification.
   * packed block of **three 32-bit unsigned integer values (12 bytes total)**,
   * given in the same order as the arguments for {@link GPUComputePassEncoder#dispatchWorkgroups}.
   * For example:
   * @param indirectBuffer - Buffer containing the indirect dispatch parameters.
   * @param indirectOffset - Offset in bytes into `indirectBuffer` where the dispatch data begins.
   */
  dispatchWorkgroupsIndirect(
    indirectBuffer: GPUBuffer,
    indirectOffset: GPUSize64
  ): undefined;
  /**
   * Completes recording of the compute pass commands sequence.
   */
  end(): undefined;
}

declare var GPUComputePassEncoder: {
  prototype: GPUComputePassEncoder;
  new (): never;
};

interface GPUComputePipeline
  extends GPUObjectBase,
    GPUPipelineBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUComputePipeline";
}

declare var GPUComputePipeline: {
  prototype: GPUComputePipeline;
  new (): never;
};

interface GPUDevice
  extends EventTarget,
    GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUDevice";
  /**
   * A set containing the {@link GPUFeatureName} values of the features
   * supported by the device (i.e. the ones with which it was created).
   */
  readonly features: GPUSupportedFeatures;
  /**
   * Exposes the limits supported by the device
   * (which are exactly the ones with which it was created).
   */
  readonly limits: GPUSupportedLimits;
  /**
   * Information about the physical adapter which created the device
   * that this GPUDevice refers to.
   * For a given GPUDevice, the GPUAdapterInfo values exposed are constant
   * over time.
   */
  readonly adapterInfo: GPUAdapterInfo;
  /**
   * The primary {@link GPUQueue} for this device.
   */
  readonly queue: GPUQueue;
  /**
   * Destroys the device, preventing further operations on it.
   * Outstanding asynchronous operations will fail.
   * Note: It is valid to destroy a device multiple times.
   * Note: Since no further operations can be enqueued on this device, implementations can abort
   * outstanding asynchronous operations immediately and free resource allocations, including
   * mapped memory that was just unmapped.
   */
  destroy(): undefined;
  /**
   * Creates a {@link GPUBuffer}.
   * @param descriptor - Description of the {@link GPUBuffer} to create.
   */
  createBuffer(
    descriptor: GPUBufferDescriptor
  ): GPUBuffer;
  /**
   * Creates a {@link GPUTexture}.
   * @param descriptor - Description of the {@link GPUTexture} to create.
   */
  createTexture(
    descriptor: GPUTextureDescriptor
  ): GPUTexture;
  /**
   * Creates a {@link GPUSampler}.
   * @param descriptor - Description of the {@link GPUSampler} to create.
   */
  createSampler(
    descriptor?: GPUSamplerDescriptor
  ): GPUSampler;
  /**
   * Creates a {@link GPUExternalTexture} wrapping the provided image source.
   * @param descriptor - Provides the external image source object (and any creation options).
   */
  importExternalTexture(
    descriptor: GPUExternalTextureDescriptor
  ): GPUExternalTexture;
  /**
   * Creates a {@link GPUBindGroupLayout}.
   * @param descriptor - Description of the {@link GPUBindGroupLayout} to create.
   */
  createBindGroupLayout(
    descriptor: GPUBindGroupLayoutDescriptor
  ): GPUBindGroupLayout;
  /**
   * Creates a {@link GPUPipelineLayout}.
   * @param descriptor - Description of the {@link GPUPipelineLayout} to create.
   */
  createPipelineLayout(
    descriptor: GPUPipelineLayoutDescriptor
  ): GPUPipelineLayout;
  /**
   * Creates a {@link GPUBindGroup}.
   * @param descriptor - Description of the {@link GPUBindGroup} to create.
   */
  createBindGroup(
    descriptor: GPUBindGroupDescriptor
  ): GPUBindGroup;
  /**
   * Creates a {@link GPUShaderModule}.
   * @param descriptor - Description of the {@link GPUShaderModule} to create.
   */
  createShaderModule(
    descriptor: GPUShaderModuleDescriptor
  ): GPUShaderModule;
  /**
   * Creates a {@link GPUComputePipeline} using immediate pipeline creation.
   * @param descriptor - Description of the {@link GPUComputePipeline} to create.
   */
  createComputePipeline(
    descriptor: GPUComputePipelineDescriptor
  ): GPUComputePipeline;
  /**
   * Creates a {@link GPURenderPipeline} using immediate pipeline creation.
   * @param descriptor - Description of the {@link GPURenderPipeline} to create.
   */
  createRenderPipeline(
    descriptor: GPURenderPipelineDescriptor
  ): GPURenderPipeline;
  /**
   * Creates a {@link GPUComputePipeline} using async pipeline creation.
   * The returned Promise resolves when the created pipeline
   * is ready to be used without additional delay.
   * If pipeline creation fails, the returned Promise rejects with an {@link GPUPipelineError}.
   * (A {@link GPUError} is not dispatched to the device.)
   * Note: Use of this method is preferred whenever possible, as it prevents blocking the
   * queue timeline work on pipeline compilation.
   * @param descriptor - Description of the {@link GPUComputePipeline} to create.
   */
  createComputePipelineAsync(
    descriptor: GPUComputePipelineDescriptor
  ): Promise<GPUComputePipeline>;
  /**
   * Creates a {@link GPURenderPipeline} using async pipeline creation.
   * The returned Promise resolves when the created pipeline
   * is ready to be used without additional delay.
   * If pipeline creation fails, the returned Promise rejects with an {@link GPUPipelineError}.
   * (A {@link GPUError} is not dispatched to the device.)
   * Note: Use of this method is preferred whenever possible, as it prevents blocking the
   * queue timeline work on pipeline compilation.
   * @param descriptor - Description of the {@link GPURenderPipeline} to create.
   */
  createRenderPipelineAsync(
    descriptor: GPURenderPipelineDescriptor
  ): Promise<GPURenderPipeline>;
  /**
   * Creates a {@link GPUCommandEncoder}.
   * @param descriptor - Description of the {@link GPUCommandEncoder} to create.
   */
  createCommandEncoder(
    descriptor?: GPUCommandEncoderDescriptor
  ): GPUCommandEncoder;
  /**
   * Creates a {@link GPURenderBundleEncoder}.
   * @param descriptor - Description of the {@link GPURenderBundleEncoder} to create.
   */
  createRenderBundleEncoder(
    descriptor: GPURenderBundleEncoderDescriptor
  ): GPURenderBundleEncoder;
  /**
   * Creates a {@link GPUQuerySet}.
   * @param descriptor - Description of the {@link GPUQuerySet} to create.
   */
  createQuerySet(
    descriptor: GPUQuerySetDescriptor
  ): GPUQuerySet;
  /**
   * A slot-backed attribute holding a promise which is created with the device, remains
   * pending for the lifetime of the device, then resolves when the device is lost.
   * Upon initialization, it is set to a new promise.
   */
  readonly lost: Promise<GPUDeviceLostInfo>;
  /**
   * Pushes a new GPU error scope onto the {@link GPUDevice}.`[[errorScopeStack]]` for `this`.
   * @param filter - Which class of errors this error scope observes.
   */
  pushErrorScope(
    filter: GPUErrorFilter
  ): undefined;
  /**
   * Pops a GPU error scope off the {@link GPUDevice}.`[[errorScopeStack]]` for `this`
   * and resolves to **any** {@link GPUError} observed by the error scope, or `null` if none.
   * There is no guarantee of the ordering of promise resolution.
   */
  popErrorScope(): Promise<GPUError | null>;
  /**
   * An event handler IDL attribute for the `uncapturederror` event type.
   */
  onuncapturederror:
    | ((
        this: GPUDevice,
        ev: GPUUncapturedErrorEvent
      ) => any)
    | null;
}

declare var GPUDevice: {
  prototype: GPUDevice;
  new (): never;
};

interface GPUDeviceLostInfo {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUDeviceLostInfo";
  readonly reason: GPUDeviceLostReason;
  readonly message: string;
}

declare var GPUDeviceLostInfo: {
  prototype: GPUDeviceLostInfo;
  new (): never;
};

interface GPUError {
  /**
   * A human-readable, localizable text message providing information about the error that
   * occurred.
   * Note: This message is generally intended for application developers to debug their
   * applications and capture information for debug reports, not to be surfaced to end-users.
   * Note: User agents should not include potentially machine-parsable details in this message,
   * such as free system memory on {@link GPUErrorFilter} `"out-of-memory"` or other details about the
   * conditions under which memory was exhausted.
   * Note: The {@link GPUError#message} should follow the best practices for language and
   * direction information. This includes making use of any future standards which may emerge
   * regarding the reporting of string language and direction metadata.
   * <p class="note editorial"><span class=marker>Editorial note:</span>
   * At the time of this writing, no language/direction recommendation is available that provides
   * compatibility and consistency with legacy APIs, but when there is, adopt it formally.
   */
  readonly message: string;
}

declare var GPUError: {
  prototype: GPUError;
  new (): never;
};

interface GPUExternalTexture
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUExternalTexture";
}

declare var GPUExternalTexture: {
  prototype: GPUExternalTexture;
  new (): never;
};

interface GPUInternalError
  extends GPUError {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUInternalError";
}

declare var GPUInternalError: {
  prototype: GPUInternalError;
  new (
    message: string
  ): GPUInternalError;
};

interface GPUOutOfMemoryError
  extends GPUError {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUOutOfMemoryError";
}

declare var GPUOutOfMemoryError: {
  prototype: GPUOutOfMemoryError;
  new (
    message: string
  ): GPUOutOfMemoryError;
};

interface GPUPipelineError
  extends DOMException {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUPipelineError";
  /**
   * A read-only slot-backed attribute exposing the type of error encountered in pipeline creation
   * as a <dfn enum for="">GPUPipelineErrorReason</dfn>:
   * <ul dfn-type=enum-value dfn-for=GPUPipelineErrorReason>
   * - <dfn>"validation"</dfn>: A [$validation error$].
   * - <dfn>"internal"</dfn>: An [$internal error$].
   * </ul>
   */
  readonly reason: GPUPipelineErrorReason;
}

declare var GPUPipelineError: {
  prototype: GPUPipelineError;
  new (
    message:
      | string
      | undefined,
    options: GPUPipelineErrorInit
  ): GPUPipelineError;
};

interface GPUPipelineLayout
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUPipelineLayout";
}

declare var GPUPipelineLayout: {
  prototype: GPUPipelineLayout;
  new (): never;
};

interface GPUQuerySet
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUQuerySet";
  /**
   * Destroys the {@link GPUQuerySet}.
   */
  destroy(): undefined;
  /**
   * The type of the queries managed by this {@link GPUQuerySet}.
   */
  readonly type: GPUQueryType;
  /**
   * The number of queries managed by this {@link GPUQuerySet}.
   */
  readonly count: GPUSize32Out;
}

declare var GPUQuerySet: {
  prototype: GPUQuerySet;
  new (): never;
};

interface GPUQueue
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUQueue";
  /**
   * Schedules the execution of the command buffers by the GPU on this queue.
   * Submitted command buffers cannot be used again.
   * 	`commandBuffers`:
   */
  submit(
    commandBuffers: Iterable<GPUCommandBuffer>
  ): undefined;
  /**
   * Returns a Promise that resolves once this queue finishes processing all the work submitted
   * up to this moment.
   * Resolution of this Promise implies the completion of
   * {@link GPUBuffer#mapAsync} calls made prior to that call,
   * on {@link GPUBuffer}s last used exclusively on that queue.
   */
  onSubmittedWorkDone(): Promise<undefined>;
  /**
   * Issues a write operation of the provided data into a {@link GPUBuffer}.
   * @param buffer - The buffer to write to.
   * @param bufferOffset - Offset in bytes into `buffer` to begin writing at.
   * @param data - Data to write into `buffer`.
   * @param dataOffset - Offset in into `data` to begin writing from. Given in elements if
   * 	`data` is a `TypedArray` and bytes otherwise.
   * @param size - Size of content to write from `data` to `buffer`. Given in elements if
   * 	`data` is a `TypedArray` and bytes otherwise.
   */
  writeBuffer(
    buffer: GPUBuffer,
    bufferOffset: GPUSize64,
    data: GPUAllowSharedBufferSource,
    dataOffset?: GPUSize64,
    size?: GPUSize64
  ): undefined;
  /**
   * Issues a write operation of the provided data into a {@link GPUTexture}.
   * @param destination - The texture subresource and origin to write to.
   * @param data - Data to write into `destination`.
   * @param dataLayout - Layout of the content in `data`.
   * @param size - Extents of the content to write from `data` to `destination`.
   */
  writeTexture(
    destination: GPUTexelCopyTextureInfo,
    data: GPUAllowSharedBufferSource,
    dataLayout: GPUTexelCopyBufferLayout,
    size: GPUExtent3DStrict
  ): undefined;
  /**
   * Issues a copy operation of the contents of a platform image/canvas
   * into the destination texture.
   * This operation performs {@link https://www.w3.org/TR/webgpu/#color-space-conversions | color encoding} into the destination
   * encoding according to the parameters of {@link GPUCopyExternalImageDestInfo}.
   * Copying into a `-srgb` texture results in the same texture bytes, not the same decoded
   * values, as copying into the corresponding non-`-srgb` format.
   * Thus, after a copy operation, sampling the destination texture has
   * different results depending on whether its format is `-srgb`, all else unchanged.
   * <!-- POSTV1(srgb-linear): If added, explain here how it interacts. -->
   * @param source - source image and origin to copy to `destination`.
   * @param destination - The texture subresource and origin to write to, and its encoding metadata.
   * @param copySize - Extents of the content to write from `source` to `destination`.
   */
  copyExternalImageToTexture(
    source: GPUCopyExternalImageSourceInfo,
    destination: GPUCopyExternalImageDestInfo,
    copySize: GPUExtent3DStrict
  ): undefined;
}

declare var GPUQueue: {
  prototype: GPUQueue;
  new (): never;
};

interface GPURenderBundle
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPURenderBundle";
}

declare var GPURenderBundle: {
  prototype: GPURenderBundle;
  new (): never;
};

interface GPURenderBundleEncoder
  extends GPUObjectBase,
    GPUCommandsMixin,
    GPUDebugCommandsMixin,
    GPUBindingCommandsMixin,
    GPURenderCommandsMixin {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPURenderBundleEncoder";
  /**
   * Completes recording of the render bundle commands sequence.
   * 	descriptor:
   */
  finish(
    descriptor?: GPURenderBundleDescriptor
  ): GPURenderBundle;
}

declare var GPURenderBundleEncoder: {
  prototype: GPURenderBundleEncoder;
  new (): never;
};

interface GPURenderPassEncoder
  extends GPUObjectBase,
    GPUCommandsMixin,
    GPUDebugCommandsMixin,
    GPUBindingCommandsMixin,
    GPURenderCommandsMixin {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPURenderPassEncoder";
  /**
   * Sets the viewport used during the rasterization stage to linearly map from
   * NDC|normalized device coordinates to viewport coordinates.
   * @param x - Minimum X value of the viewport in pixels.
   * @param y - Minimum Y value of the viewport in pixels.
   * @param width - Width of the viewport in pixels.
   * @param height - Height of the viewport in pixels.
   * @param minDepth - Minimum depth value of the viewport.
   * @param maxDepth - Maximum depth value of the viewport.
   */
  setViewport(
    x: number,
    y: number,
    width: number,
    height: number,
    minDepth: number,
    maxDepth: number
  ): undefined;
  /**
   * Sets the scissor rectangle used during the rasterization stage.
   * After transformation into viewport coordinates any fragments which fall outside the scissor
   * rectangle will be discarded.
   * @param x - Minimum X value of the scissor rectangle in pixels.
   * @param y - Minimum Y value of the scissor rectangle in pixels.
   * @param width - Width of the scissor rectangle in pixels.
   * @param height - Height of the scissor rectangle in pixels.
   */
  setScissorRect(
    x: GPUIntegerCoordinate,
    y: GPUIntegerCoordinate,
    width: GPUIntegerCoordinate,
    height: GPUIntegerCoordinate
  ): undefined;
  /**
   * Sets the constant blend color and alpha values used with {@link GPUBlendFactor} `"constant"`
   * and {@link GPUBlendFactor} `"one-minus-constant"` {@link GPUBlendFactor}s.
   * @param color - The color to use when blending.
   */
  setBlendConstant(
    color: GPUColor
  ): undefined;
  /**
   * Sets the RenderState.`[[stencilReference]]` value used during stencil tests with
   * the {@link GPUStencilOperation} `"replace"` {@link GPUStencilOperation}.
   * @param reference - The new stencil reference value.
   */
  setStencilReference(
    reference: GPUStencilValue
  ): undefined;
  /**
   * @param queryIndex - The index of the query in the query set.
   */
  beginOcclusionQuery(
    queryIndex: GPUSize32
  ): undefined;
  /**
   */
  endOcclusionQuery(): undefined;
  /**
   * Executes the commands previously recorded into the given {@link GPURenderBundle}s as part of
   * this render pass.
   * When a {@link GPURenderBundle} is executed, it does not inherit the render pass's pipeline, bind
   * groups, or vertex and index buffers. After a {@link GPURenderBundle} has executed, the render
   * pass's pipeline, bind group, and vertex/index buffer state is cleared
   * (to the initial, empty values).
   * Note: The state is cleared, not restored to the previous state.
   * This occurs even if zero {@link GPURenderBundle | GPURenderBundles} are executed.
   * @param bundles - List of render bundles to execute.
   */
  executeBundles(
    bundles: Iterable<GPURenderBundle>
  ): undefined;
  /**
   * Completes recording of the render pass commands sequence.
   */
  end(): undefined;
}

declare var GPURenderPassEncoder: {
  prototype: GPURenderPassEncoder;
  new (): never;
};

interface GPURenderPipeline
  extends GPUObjectBase,
    GPUPipelineBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPURenderPipeline";
}

declare var GPURenderPipeline: {
  prototype: GPURenderPipeline;
  new (): never;
};

interface GPUSampler
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUSampler";
}

declare var GPUSampler: {
  prototype: GPUSampler;
  new (): never;
};

interface GPUShaderModule
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUShaderModule";
  /**
   * Returns any messages generated during the {@link GPUShaderModule}'s compilation.
   * The locations, order, and contents of messages are implementation-defined
   * In particular, messages may not be ordered by {@link GPUCompilationMessage#lineNum}.
   */
  getCompilationInfo(): Promise<GPUCompilationInfo>;
}

declare var GPUShaderModule: {
  prototype: GPUShaderModule;
  new (): never;
};

type GPUSupportedFeatures =
  ReadonlySet<string>;

interface GPUSupportedLimits {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUSupportedLimits";
  readonly maxTextureDimension1D: number;
  readonly maxTextureDimension2D: number;
  readonly maxTextureDimension3D: number;
  readonly maxTextureArrayLayers: number;
  readonly maxBindGroups: number;
  readonly maxBindGroupsPlusVertexBuffers: number;
  readonly maxBindingsPerBindGroup: number;
  readonly maxDynamicUniformBuffersPerPipelineLayout: number;
  readonly maxDynamicStorageBuffersPerPipelineLayout: number;
  readonly maxSampledTexturesPerShaderStage: number;
  readonly maxSamplersPerShaderStage: number;
  readonly maxStorageBuffersPerShaderStage: number;
  readonly maxStorageTexturesPerShaderStage: number;
  readonly maxUniformBuffersPerShaderStage: number;
  readonly maxUniformBufferBindingSize: number;
  readonly maxStorageBufferBindingSize: number;
  readonly minUniformBufferOffsetAlignment: number;
  readonly minStorageBufferOffsetAlignment: number;
  readonly maxVertexBuffers: number;
  readonly maxBufferSize: number;
  readonly maxVertexAttributes: number;
  readonly maxVertexBufferArrayStride: number;
  readonly maxInterStageShaderVariables: number;
  readonly maxColorAttachments: number;
  readonly maxColorAttachmentBytesPerSample: number;
  readonly maxComputeWorkgroupStorageSize: number;
  readonly maxComputeInvocationsPerWorkgroup: number;
  readonly maxComputeWorkgroupSizeX: number;
  readonly maxComputeWorkgroupSizeY: number;
  readonly maxComputeWorkgroupSizeZ: number;
  readonly maxComputeWorkgroupsPerDimension: number;
  /** **PROPOSED** in [Compatibility Mode](https://github.com/gpuweb/gpuweb/blob/main/proposals/compatibility-mode.md). */
  readonly maxStorageBuffersInVertexStage?: number;
  /** **PROPOSED** in [Compatibility Mode](https://github.com/gpuweb/gpuweb/blob/main/proposals/compatibility-mode.md). */
  readonly maxStorageBuffersInFragmentStage?: number;
  /** **PROPOSED** in [Compatibility Mode](https://github.com/gpuweb/gpuweb/blob/main/proposals/compatibility-mode.md). */
  readonly maxStorageTexturesInVertexStage?: number;
  /** **PROPOSED** in [Compatibility Mode](https://github.com/gpuweb/gpuweb/blob/main/proposals/compatibility-mode.md). */
  readonly maxStorageTexturesInFragmentStage?: number;
}

declare var GPUSupportedLimits: {
  prototype: GPUSupportedLimits;
};

interface GPUTexture
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUTexture";
  /**
   * Creates a {@link GPUTextureView}.
   * @param descriptor - Description of the {@link GPUTextureView} to create.
   */
  createView(
    descriptor?: GPUTextureViewDescriptor
  ): GPUTextureView;
  /**
   * Destroys the {@link GPUTexture}.
   */
  destroy(): undefined;
  /**
   * The width of this {@link GPUTexture}.
   */
  readonly width: GPUIntegerCoordinateOut;
  /**
   * The height of this {@link GPUTexture}.
   */
  readonly height: GPUIntegerCoordinateOut;
  /**
   * The depth or layer count of this {@link GPUTexture}.
   */
  readonly depthOrArrayLayers: GPUIntegerCoordinateOut;
  /**
   * The number of mip levels of this {@link GPUTexture}.
   */
  readonly mipLevelCount: GPUIntegerCoordinateOut;
  /**
   * The number of sample count of this {@link GPUTexture}.
   */
  readonly sampleCount: GPUSize32Out;
  /**
   * The dimension of the set of texel for each of this {@link GPUTexture}'s subresources.
   */
  readonly dimension: GPUTextureDimension;
  /**
   * The format of this {@link GPUTexture}.
   */
  readonly format: GPUTextureFormat;
  /**
   * The allowed usages for this {@link GPUTexture}.
   */
  readonly usage: GPUFlagsConstant;
}

declare var GPUTexture: {
  prototype: GPUTexture;
  new (): never;
};

interface GPUTextureView
  extends GPUObjectBase {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUTextureView";
}

declare var GPUTextureView: {
  prototype: GPUTextureView;
  new (): never;
};

interface GPUUncapturedErrorEvent
  extends Event {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUUncapturedErrorEvent";
  /**
   * A slot-backed attribute holding an object representing the error that was uncaptured.
   * This has the same type as errors returned by {@link GPUDevice#popErrorScope}.
   */
  readonly error: GPUError;
}

declare var GPUUncapturedErrorEvent: {
  prototype: GPUUncapturedErrorEvent;
  new (
    type: string,
    gpuUncapturedErrorEventInitDict: GPUUncapturedErrorEventInit
  ): GPUUncapturedErrorEvent;
};

interface GPUValidationError
  extends GPUError {
  /** @internal Workaround for [nominal typing](https://github.com/microsoft/TypeScript/pull/33038). */
  readonly __brand: "GPUValidationError";
}

declare var GPUValidationError: {
  prototype: GPUValidationError;
  new (
    message: string
  ): GPUValidationError;
};

type WGSLLanguageFeatures =
  ReadonlySet<string>;

interface Navigator
  extends NavigatorGPU {}

interface WorkerNavigator
  extends NavigatorGPU {}

interface GPUBufferUsage {
  readonly MAP_READ: GPUFlagsConstant;
  readonly MAP_WRITE: GPUFlagsConstant;
  readonly COPY_SRC: GPUFlagsConstant;
  readonly COPY_DST: GPUFlagsConstant;
  readonly INDEX: GPUFlagsConstant;
  readonly VERTEX: GPUFlagsConstant;
  readonly UNIFORM: GPUFlagsConstant;
  readonly STORAGE: GPUFlagsConstant;
  readonly INDIRECT: GPUFlagsConstant;
  readonly QUERY_RESOLVE: GPUFlagsConstant;
}

declare var GPUBufferUsage: GPUBufferUsage;

interface GPUColorWrite {
  readonly RED: GPUFlagsConstant;
  readonly GREEN: GPUFlagsConstant;
  readonly BLUE: GPUFlagsConstant;
  readonly ALPHA: GPUFlagsConstant;
  readonly ALL: GPUFlagsConstant;
}

declare var GPUColorWrite: GPUColorWrite;

interface GPUMapMode {
  readonly READ: GPUFlagsConstant;
  readonly WRITE: GPUFlagsConstant;
}

declare var GPUMapMode: GPUMapMode;

interface GPUShaderStage {
  readonly VERTEX: GPUFlagsConstant;
  readonly FRAGMENT: GPUFlagsConstant;
  readonly COMPUTE: GPUFlagsConstant;
}

declare var GPUShaderStage: GPUShaderStage;

interface GPUTextureUsage {
  readonly COPY_SRC: GPUFlagsConstant;
  readonly COPY_DST: GPUFlagsConstant;
  readonly TEXTURE_BINDING: GPUFlagsConstant;
  readonly STORAGE_BINDING: GPUFlagsConstant;
  readonly RENDER_ATTACHMENT: GPUFlagsConstant;
}

declare var GPUTextureUsage: GPUTextureUsage;

/** @deprecated Use {@link GPUTexelCopyBufferLayout} */
type GPUImageDataLayout =
  GPUTexelCopyBufferLayout;
/** @deprecated Use {@link GPUTexelCopyBufferInfo} */
type GPUImageCopyBuffer =
  GPUTexelCopyBufferInfo;
/** @deprecated Use {@link GPUTexelCopyTextureInfo} */
type GPUImageCopyTexture =
  GPUTexelCopyTextureInfo;
/** @deprecated Use {@link GPUCopyExternalImageDestInfo} */
type GPUImageCopyTextureTagged =
  GPUCopyExternalImageDestInfo;
/** @deprecated Use {@link GPUCopyExternalImageSourceInfo} */
type GPUImageCopyExternalImage =
  GPUCopyExternalImageSourceInfo;
/** @deprecated Use {@link GPUCopyExternalImageSource} */
type GPUImageCopyExternalImageSource =
  GPUCopyExternalImageSource;
