# Testing Documentation

## Overview
This directory contains comprehensive test suites for the refactored 3D graphics library components.

## Setup and Installation

### Prerequisites
- Node.js (version 14 or higher)
- npm (comes with Node.js)

### Installation
The testing framework and dependencies are already configured. If you need to reinstall:

```bash
npm install
```

### Dependencies
- **Jest**: Testing framework
- **Babel**: ES6 module transpilation for Node.js
- **@babel/core**: Babel core functionality
- **@babel/preset-env**: Babel preset for modern JavaScript
- **babel-jest**: Jest transformer for Babel

## Running Tests

### Basic Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode (re-runs on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Test File Structure

```
tests/
├── README.md                 # This file
├── Vector4.test.js          # Comprehensive Vector4 tests (67 test cases)
└── helpers/
    └── testUtils.js         # Test utilities and helper functions
```

## Test Organization

### Vector4.test.js
Contains 67 comprehensive test cases organized into the following categories:

1. **Constructor** - Testing object creation
2. **Properties and Getters/Setters** - Property access and modification
3. **Basic Operations** - Core functionality like set, setScalar, setComponent
4. **Clone and Copy** - Object duplication and copying
5. **Mathematical Operations** - Addition, subtraction, multiplication, division
6. **Matrix Operations** - Matrix transformations
7. **Axis-Angle Operations** - Quaternion and rotation matrix conversions
8. **Min/Max Operations** - Component-wise minimum and maximum
9. **Clamping Operations** - Value and length clamping
10. **Rounding Operations** - Floor, ceil, round, roundToZero
11. **Vector Operations** - Dot product, length, normalization
12. **Interpolation** - Linear interpolation between vectors
13. **Equality and Comparison** - Vector equality testing
14. **Array Operations** - Array conversion and population
15. **Buffer Attribute Operations** - WebGL buffer attribute integration
16. **Random Operations** - Random value generation
17. **Edge Cases and Error Handling** - Boundary conditions and error states
18. **Method Chaining** - Fluent interface verification
19. **Deprecated Method Warnings** - Legacy API deprecation notices

### Test Utilities (helpers/testUtils.js)
Provides helper functions for testing:

- `createMockMatrix4()` - Creates mock Matrix4 objects
- `createTranslationMatrix()` - Creates translation matrices
- `createScaleMatrix()` - Creates scale matrices
- `createMockQuaternion()` - Creates mock quaternion objects
- `createMockBufferAttribute()` - Creates mock buffer attributes
- `approximately()` - Floating-point comparison with tolerance
- `vectorsApproximatelyEqual()` - Vector equality with tolerance

## Test Coverage

Current coverage for Vector4.js:
- **Statement Coverage**: 96.21%
- **Branch Coverage**: 86.76%
- **Function Coverage**: 100%
- **Line Coverage**: 96.4%

### Coverage Reports
Coverage reports are generated in the `coverage/` directory when running `npm run test:coverage`. Open `coverage/lcov-report/index.html` in a browser to view detailed coverage information.

## Writing New Tests

### Test Structure
Follow this pattern for new test files:

```javascript
import { ClassName } from '../src/path/to/ClassName.js';

describe('ClassName', () => {
  let instance;

  beforeEach(() => {
    instance = new ClassName();
  });

  describe('Method Group', () => {
    test('should do something specific', () => {
      // Arrange
      const input = 'test input';
      
      // Act
      const result = instance.method(input);
      
      // Assert
      expect(result).toBe('expected output');
    });
  });
});
```

### Best Practices

1. **Descriptive Test Names**: Use clear, descriptive test names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification phases
3. **Isolation**: Each test should be independent and not rely on other tests
4. **Edge Cases**: Include tests for boundary conditions, error states, and edge cases
5. **Precision**: Use appropriate precision for floating-point comparisons (`toBeCloseTo()`)
6. **Mocking**: Use Jest mocks for external dependencies and complex objects

### Floating-Point Comparisons
When testing mathematical operations, use `toBeCloseTo()` for floating-point comparisons:

```javascript
expect(result).toBeCloseTo(expectedValue, 5); // 5 decimal places precision
```

### Error Testing
Test error conditions using `toThrow()`:

```javascript
expect(() => vector.getComponent(5)).toThrow('index is out of range: 5');
```

### Console Warning Testing
Test deprecation warnings by mocking console.warn:

```javascript
const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
// ... code that should trigger warning
expect(consoleSpy).toHaveBeenCalledWith('Expected warning message');
consoleSpy.mockRestore();
```

## Configuration Files

### package.json
Contains Jest configuration:
- Test environment: Node.js
- Transform: babel-jest for ES6 modules
- Test patterns: `**/tests/**/*.test.js` and `**/__tests__/**/*.test.js`
- Coverage collection from `src/**/*.js` files

### babel.config.js
Babel configuration for ES6 module support:
- Preset: @babel/preset-env targeting current Node.js version
- Enables Jest to work with ES6 import/export syntax

## Troubleshooting

### Common Issues

1. **ES6 Module Errors**: Ensure babel.config.js is properly configured
2. **Import Errors**: Check file paths in import statements
3. **Test Timeouts**: Increase timeout for complex mathematical operations
4. **Coverage Issues**: Ensure all source files are included in coverage collection

### Debug Mode
Run tests with additional debugging:

```bash
# Run with verbose output
npm test -- --verbose

# Run specific test file
npm test -- Vector4.test.js

# Run tests matching pattern
npm test -- --testNamePattern="should handle"
```

## Future Testing

### Planned Test Suites
- Vector2.test.js - 2D vector operations
- Vector3.test.js - 3D vector operations  
- Matrix4.test.js - 4x4 matrix operations
- Quaternion.test.js - Quaternion operations

### Integration Tests
Consider adding integration tests that verify:
- Cross-component interactions
- Performance benchmarks
- WebGL integration
- Real-world usage scenarios

## Contributing

When adding new tests:
1. Follow the existing test structure and naming conventions
2. Ensure new tests pass and don't break existing tests
3. Update coverage expectations if adding new functionality
4. Document any new test utilities or patterns
5. Run the full test suite before submitting changes
