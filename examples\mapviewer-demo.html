<!doctype html>
<html>
<head>
    <title>VisioWeb Essential - Refactored Demo</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- UIkit CSS/JS -->
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.1/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.1/dist/js/uikit-icons.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.7.1/dist/css/uikit.min.css" />

    <!-- Lodash -->
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>

    <!-- VisioWeb Core Library (external dependency) -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/visioglobe/VisioWeb@latest/build/visioweb.js"></script>
    
    <!-- Our Refactored VisioWeb Essential Library -->
    <script type="module" src="js/mapviewer-demo.js"></script>
    
    <style>
        /* Basic styling for the demo */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .toolbar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .toolbar button {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .toolbar button:hover {
            background: #0056b3;
        }
        
        .toolbar button:disabled {
            background: #6c757d;
            border-color: #6c757d;
            cursor: not-allowed;
        }
        
        .toolbar select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        #container {
            width: 100vw;
            height: calc(100vh - 60px);
            position: relative;
        }
        
        .progress-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .progress-bar {
            width: 300px;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 300px;
        }
        
        .place-bubble {
            position: absolute;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 250px;
            display: none;
        }
        
        .navigation-panel {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .status {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Toolbar -->
    <div class="toolbar">
        <h3 style="margin: 0;">VisioWeb Essential Demo</h3>
        <button id="globalBtn">Global View</button>
        <select id="buildingSelect">
            <option value="">Select Building</option>
        </select>
        <select id="floorSelect">
            <option value="">Select Floor</option>
        </select>
        <button id="createPlaceBtn">Create Sample Place</button>
        <button id="routeBtn" disabled>Create Route</button>
        <button id="clearRouteBtn" disabled>Clear Route</button>
    </div>

    <!-- Map Container -->
    <div id="container"></div>

    <!-- Progress Overlay -->
    <div id="progressOverlay" class="progress-overlay">
        <div>Loading VisioWeb...</div>
        <div class="progress-bar">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div id="progressText">0%</div>
    </div>

    <!-- Info Panel -->
    <div class="info-panel">
        <h4>Library Status</h4>
        <div id="libraryStatus">Initializing...</div>
        <div id="mapviewerStatus">Not loaded</div>
        <div id="venueInfo"></div>
    </div>

    <!-- Place Bubble -->
    <div id="placeBubble" class="place-bubble">
        <h4 id="placeTitle">Place Name</h4>
        <p id="placeDescription">Place description</p>
        <button id="setFromBtn">Set as Start</button>
        <button id="setToBtn">Set as Destination</button>
        <button id="closeBubbleBtn">Close</button>
    </div>

    <!-- Navigation Panel -->
    <div id="navigationPanel" class="navigation-panel">
        <h4>Navigation</h4>
        <div id="instructionText">Follow the route</div>
        <button id="prevInstructionBtn">Previous</button>
        <button id="nextInstructionBtn">Next</button>
    </div>
</body>
</html>
