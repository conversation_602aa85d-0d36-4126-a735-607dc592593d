
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Vector2.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> Vector2.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/54</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/143</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @fileoverview Vector2 - 2D vector class
 * <AUTHOR> Authors
 */
&nbsp;
/**
 * 2D vector class with x and y components
 */
class Vector2 {
<span class="fstat-no" title="function not covered" >    co</span>nstructor(x = <span class="branch-0 cbranch-no" title="branch not covered" >0,</span> y = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >        Object.defineProperty(this, "isVector2", {</span>
            value: true
        });
<span class="cstat-no" title="statement not covered" >        this.x = x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = y;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t width() {
<span class="cstat-no" title="statement not covered" >        return this.x;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t width(value) {
<span class="cstat-no" title="statement not covered" >        this.x = value;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t height() {
<span class="cstat-no" title="statement not covered" >        return this.y;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t height(value) {
<span class="cstat-no" title="statement not covered" >        this.y = value;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t(x, y) {
<span class="cstat-no" title="statement not covered" >        this.x = x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tScalar(scalar) {
<span class="cstat-no" title="statement not covered" >        this.x = scalar;</span>
<span class="cstat-no" title="statement not covered" >        this.y = scalar;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tX(x) {
<span class="cstat-no" title="statement not covered" >        this.x = x;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tY(y) {
<span class="cstat-no" title="statement not covered" >        this.y = y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tComponent(index, value) {
<span class="cstat-no" title="statement not covered" >        switch (index) {</span>
            case 0:
<span class="cstat-no" title="statement not covered" >                this.x = value;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 1:
<span class="cstat-no" title="statement not covered" >                this.y = value;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            default:
<span class="cstat-no" title="statement not covered" >                throw new Error("index is out of range: " + index);</span>
        }
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>tComponent(index) {
<span class="cstat-no" title="statement not covered" >        switch (index) {</span>
            case 0:
<span class="cstat-no" title="statement not covered" >                return this.x;</span>
            case 1:
<span class="cstat-no" title="statement not covered" >                return this.y;</span>
            default:
<span class="cstat-no" title="statement not covered" >                throw new Error("index is out of range: " + index);</span>
        }
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>one() {
<span class="cstat-no" title="statement not covered" >        return new this.constructor(this.x, this.y);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    co</span>py(v) {
<span class="cstat-no" title="statement not covered" >        this.x = v.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = v.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ad</span>d(v, w) {
<span class="cstat-no" title="statement not covered" >        if (w !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn("THREE.Vector2: .add() now only accepts one argument. Use .addVectors( a, b ) instead.");</span>
<span class="cstat-no" title="statement not covered" >            return this.addVectors(v, w);</span>
        }
<span class="cstat-no" title="statement not covered" >        this.x += v.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y += v.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ad</span>dScalar(s) {
<span class="cstat-no" title="statement not covered" >        this.x += s;</span>
<span class="cstat-no" title="statement not covered" >        this.y += s;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ad</span>dVectors(a, b) {
<span class="cstat-no" title="statement not covered" >        this.x = a.x + b.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = a.y + b.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ad</span>dScaledVector(v, s) {
<span class="cstat-no" title="statement not covered" >        this.x += v.x * s;</span>
<span class="cstat-no" title="statement not covered" >        this.y += v.y * s;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    su</span>b(v, w) {
<span class="cstat-no" title="statement not covered" >        if (w !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn("THREE.Vector2: .sub() now only accepts one argument. Use .subVectors( a, b ) instead.");</span>
<span class="cstat-no" title="statement not covered" >            return this.subVectors(v, w);</span>
        }
<span class="cstat-no" title="statement not covered" >        this.x -= v.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y -= v.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    su</span>bScalar(s) {
<span class="cstat-no" title="statement not covered" >        this.x -= s;</span>
<span class="cstat-no" title="statement not covered" >        this.y -= s;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    su</span>bVectors(a, b) {
<span class="cstat-no" title="statement not covered" >        this.x = a.x - b.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = a.y - b.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiply(v) {
<span class="cstat-no" title="statement not covered" >        this.x *= v.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y *= v.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiplyScalar(scalar) {
<span class="cstat-no" title="statement not covered" >        this.x *= scalar;</span>
<span class="cstat-no" title="statement not covered" >        this.y *= scalar;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    di</span>vide(v) {
<span class="cstat-no" title="statement not covered" >        this.x /= v.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y /= v.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    di</span>videScalar(scalar) {
<span class="cstat-no" title="statement not covered" >        return this.multiplyScalar(1 / scalar);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ap</span>plyMatrix3(m) {
        const x = <span class="cstat-no" title="statement not covered" >this.x,</span> y = <span class="cstat-no" title="statement not covered" >this.y;</span>
        const e = <span class="cstat-no" title="statement not covered" >m.elements;</span>
<span class="cstat-no" title="statement not covered" >        this.x = e[0] * x + e[3] * y + e[6];</span>
<span class="cstat-no" title="statement not covered" >        this.y = e[1] * x + e[4] * y + e[7];</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mi</span>n(v) {
<span class="cstat-no" title="statement not covered" >        this.x = Math.min(this.x, v.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.min(this.y, v.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>x(v) {
<span class="cstat-no" title="statement not covered" >        this.x = Math.max(this.x, v.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.max(this.y, v.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>amp(min, max) {
<span class="cstat-no" title="statement not covered" >        this.x = Math.max(min.x, Math.min(max.x, this.x));</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.max(min.y, Math.min(max.y, this.y));</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>ampScalar(minVal, maxVal) {
<span class="cstat-no" title="statement not covered" >        this.x = Math.max(minVal, Math.min(maxVal, this.x));</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.max(minVal, Math.min(maxVal, this.y));</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>ampLength(min, max) {
        const length = <span class="cstat-no" title="statement not covered" >this.length();</span>
<span class="cstat-no" title="statement not covered" >        return this.divideScalar(length || 1).multiplyScalar(Math.max(min, Math.min(max, length)));</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    fl</span>oor() {
<span class="cstat-no" title="statement not covered" >        this.x = Math.floor(this.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.floor(this.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ce</span>il() {
<span class="cstat-no" title="statement not covered" >        this.x = Math.ceil(this.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.ceil(this.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ro</span>und() {
<span class="cstat-no" title="statement not covered" >        this.x = Math.round(this.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.round(this.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ro</span>undToZero() {
<span class="cstat-no" title="statement not covered" >        this.x = (this.x &lt; 0) ? Math.ceil(this.x) : Math.floor(this.x);</span>
<span class="cstat-no" title="statement not covered" >        this.y = (this.y &lt; 0) ? Math.ceil(this.y) : Math.floor(this.y);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ne</span>gate() {
<span class="cstat-no" title="statement not covered" >        this.x = -this.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = -this.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    do</span>t(v) {
<span class="cstat-no" title="statement not covered" >        return this.x * v.x + this.y * v.y;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cr</span>oss(v) {
<span class="cstat-no" title="statement not covered" >        return this.x * v.y - this.y * v.x;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>ngthSq() {
<span class="cstat-no" title="statement not covered" >        return this.x * this.x + this.y * this.y;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>ngth() {
<span class="cstat-no" title="statement not covered" >        return Math.sqrt(this.x * this.x + this.y * this.y);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>nhattanLength() {
<span class="cstat-no" title="statement not covered" >        return Math.abs(this.x) + Math.abs(this.y);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    no</span>rmalize() {
<span class="cstat-no" title="statement not covered" >        return this.divideScalar(this.length() || 1);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    an</span>gle() {
        const angle = <span class="cstat-no" title="statement not covered" >Math.atan2(-this.y, -this.x) + Math.PI;</span>
<span class="cstat-no" title="statement not covered" >        return angle;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    di</span>stanceTo(v) {
<span class="cstat-no" title="statement not covered" >        return Math.sqrt(this.distanceToSquared(v));</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    di</span>stanceToSquared(v) {
        const dx = <span class="cstat-no" title="statement not covered" >this.x - v.x,</span> dy = <span class="cstat-no" title="statement not covered" >this.y - v.y;</span>
<span class="cstat-no" title="statement not covered" >        return dx * dx + dy * dy;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ma</span>nhattanDistanceTo(v) {
<span class="cstat-no" title="statement not covered" >        return Math.abs(this.x - v.x) + Math.abs(this.y - v.y);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tLength(length) {
<span class="cstat-no" title="statement not covered" >        return this.normalize().multiplyScalar(length);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>rp(v, alpha) {
<span class="cstat-no" title="statement not covered" >        this.x += (v.x - this.x) * alpha;</span>
<span class="cstat-no" title="statement not covered" >        this.y += (v.y - this.y) * alpha;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>rpVectors(v1, v2, alpha) {
<span class="cstat-no" title="statement not covered" >        this.x = v1.x + (v2.x - v1.x) * alpha;</span>
<span class="cstat-no" title="statement not covered" >        this.y = v1.y + (v2.y - v1.y) * alpha;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    eq</span>uals(v) {
<span class="cstat-no" title="statement not covered" >        return ((v.x === this.x) &amp;&amp; (v.y === this.y));</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    fr</span>omArray(array, offset = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >        this.x = array[offset];</span>
<span class="cstat-no" title="statement not covered" >        this.y = array[offset + 1];</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    to</span>Array(array = <span class="branch-0 cbranch-no" title="branch not covered" >[],</span> offset = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >        array[offset] = this.x;</span>
<span class="cstat-no" title="statement not covered" >        array[offset + 1] = this.y;</span>
<span class="cstat-no" title="statement not covered" >        return array;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    fr</span>omBufferAttribute(attribute, index, offset) {
<span class="cstat-no" title="statement not covered" >        if (offset !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn("THREE.Vector2: offset has been removed from .fromBufferAttribute().");</span>
        }
<span class="cstat-no" title="statement not covered" >        this.x = attribute.getX(index);</span>
<span class="cstat-no" title="statement not covered" >        this.y = attribute.getY(index);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ro</span>tateAround(center, angle) {
        const c = <span class="cstat-no" title="statement not covered" >Math.cos(angle),</span> s = <span class="cstat-no" title="statement not covered" >Math.sin(angle);</span>
        const x = <span class="cstat-no" title="statement not covered" >this.x - center.x;</span>
        const y = <span class="cstat-no" title="statement not covered" >this.y - center.y;</span>
<span class="cstat-no" title="statement not covered" >        this.x = x * c - y * s + center.x;</span>
<span class="cstat-no" title="statement not covered" >        this.y = x * s + y * c + center.y;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ra</span>ndom() {
<span class="cstat-no" title="statement not covered" >        this.x = Math.random();</span>
<span class="cstat-no" title="statement not covered" >        this.y = Math.random();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
}
&nbsp;
export { Vector2 };
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-04T21:07:49.701Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    