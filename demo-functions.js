// Demo Functions for Three.js Integration
// This file contains all the interactive demo functions

// Math Classes Testing Functions
function testVector3() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        // Test our Vector3 class with Three.js integration
        const v1 = new Vector3(1, 2, 3);
        const v2 = new Vector3(4, 5, 6);
        
        // Test basic operations
        const sum = v1.clone().add(v2);
        const cross = v1.clone().cross(v2);
        const length = v1.length();
        
        // Test Three.js methods
        const normalized = v1.clone().normalize();
        const lerped = v1.clone().lerp(v2, 0.5);
        
        output.innerHTML = `
            <strong>Vector3 Test Results:</strong><br>
            v1: (${v1.x}, ${v1.y}, ${v1.z})<br>
            v2: (${v2.x}, ${v2.y}, ${v2.z})<br>
            v1 + v2: (${sum.x.toFixed(2)}, ${sum.y.toFixed(2)}, ${sum.z.toFixed(2)})<br>
            v1 × v2: (${cross.x.toFixed(2)}, ${cross.y.toFixed(2)}, ${cross.z.toFixed(2)})<br>
            |v1|: ${length.toFixed(2)}<br>
            normalized v1: (${normalized.x.toFixed(2)}, ${normalized.y.toFixed(2)}, ${normalized.z.toFixed(2)})<br>
            lerp(v1, v2, 0.5): (${lerped.x.toFixed(2)}, ${lerped.y.toFixed(2)}, ${lerped.z.toFixed(2)})<br>
            <span style="color: #4caf50;">✓ All Vector3 operations successful!</span>
        `;
        
        updateStatus('Vector3 test completed successfully!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Vector3 test failed: ${error.message}</span>`;
        updateStatus('Vector3 test failed', 'error');
    }
}

function testVector2() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        const v1 = new Vector2(3, 4);
        const v2 = new Vector2(1, 2);
        
        // Test basic operations
        const sum = v1.clone().add(v2);
        const length = v1.length();
        const angle = v1.angle();
        
        // Test width/height compatibility
        v1.width = 10;
        v1.height = 20;
        
        output.innerHTML = `
            <strong>Vector2 Test Results:</strong><br>
            v1: (${v1.x}, ${v1.y})<br>
            v2: (${v2.x}, ${v2.y})<br>
            v1 + v2: (${sum.x}, ${sum.y})<br>
            |v1|: ${length.toFixed(2)}<br>
            angle: ${angle.toFixed(2)} rad<br>
            width/height: (${v1.width}, ${v1.height})<br>
            <span style="color: #4caf50;">✓ All Vector2 operations successful!</span>
        `;
        
        updateStatus('Vector2 test completed successfully!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Vector2 test failed: ${error.message}</span>`;
        updateStatus('Vector2 test failed', 'error');
    }
}

function testMatrix4() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        const m1 = new Matrix4();
        const m2 = new Matrix4();
        
        // Test basic operations
        m1.makeRotationX(Math.PI / 4);
        m2.makeTranslation(1, 2, 3);
        
        const result = m1.clone().multiply(m2);
        const determinant = m1.determinant();
        
        output.innerHTML = `
            <strong>Matrix4 Test Results:</strong><br>
            Rotation X (π/4) created<br>
            Translation (1, 2, 3) created<br>
            Matrix multiplication successful<br>
            Determinant: ${determinant.toFixed(4)}<br>
            Clone method: ${result.isMatrix4 ? 'Working' : 'Failed'}<br>
            <span style="color: #4caf50;">✓ All Matrix4 operations successful!</span>
        `;
        
        updateStatus('Matrix4 test completed successfully!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Matrix4 test failed: ${error.message}</span>`;
        updateStatus('Matrix4 test failed', 'error');
    }
}

function testQuaternion() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        const q1 = new Quaternion(0, 0, 0, 1);
        const q2 = new Quaternion(0, 0, Math.sin(Math.PI/8), Math.cos(Math.PI/8));
        
        // Test basic operations
        const multiplied = q1.clone().multiply(q2);
        const length = q1.length();
        const normalized = q2.clone().normalize();
        
        output.innerHTML = `
            <strong>Quaternion Test Results:</strong><br>
            q1: (${q1.x}, ${q1.y}, ${q1.z}, ${q1.w})<br>
            q2: (${q2.x.toFixed(3)}, ${q2.y.toFixed(3)}, ${q2.z.toFixed(3)}, ${q2.w.toFixed(3)})<br>
            q1 * q2: (${multiplied.x.toFixed(3)}, ${multiplied.y.toFixed(3)}, ${multiplied.z.toFixed(3)}, ${multiplied.w.toFixed(3)})<br>
            |q1|: ${length}<br>
            normalized q2: (${normalized.x.toFixed(3)}, ${normalized.y.toFixed(3)}, ${normalized.z.toFixed(3)}, ${normalized.w.toFixed(3)})<br>
            <span style="color: #4caf50;">✓ All Quaternion operations successful!</span>
        `;
        
        updateStatus('Quaternion test completed successfully!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Quaternion test failed: ${error.message}</span>`;
        updateStatus('Quaternion test failed', 'error');
    }
}

// 3D Scene Creation Functions
function createBasicScene() {
    clearScene();
    
    try {
        // Create a rotating cube using our Vector3 class for positioning
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshPhongMaterial({ 
            color: 0x64b5f6,
            shininess: 100 
        });
        const cube = new THREE.Mesh(geometry, material);
        
        // Use our Vector3 class to set position
        const position = new Vector3(0, 0, 0);
        cube.position.copy(position);
        cube.userData.animate = true;
        cube.castShadow = true;
        cube.receiveShadow = true;
        
        scene.add(cube);
        currentObjects.push(cube);
        
        updateSceneInfo('Basic rotating cube', currentObjects.length);
        updateStatus('Basic scene created successfully!', 'success');
    } catch (error) {
        updateStatus(`Failed to create basic scene: ${error.message}`, 'error');
    }
}

function createMathDemo() {
    clearScene();
    
    try {
        // Create multiple objects to demonstrate math classes
        const colors = [0xff6b6b, 0x4ecdc4, 0x45b7d1, 0xf9ca24, 0xf0932b];
        
        for (let i = 0; i < 5; i++) {
            // Use our Vector3 class for positioning
            const position = new Vector3(
                Math.cos(i * Math.PI * 2 / 5) * 3,
                Math.sin(i * Math.PI / 3),
                Math.sin(i * Math.PI * 2 / 5) * 3
            );
            
            const geometry = new THREE.SphereGeometry(0.5, 16, 16);
            const material = new THREE.MeshPhongMaterial({ color: colors[i] });
            const sphere = new THREE.Mesh(geometry, material);
            
            sphere.position.copy(position);
            sphere.userData.animate = true;
            sphere.castShadow = true;
            
            scene.add(sphere);
            currentObjects.push(sphere);
            
            // Add connecting lines using Vector3 operations
            if (i > 0) {
                const prevPosition = currentObjects[i-1].position;
                const lineGeometry = new THREE.BufferGeometry().setFromPoints([
                    prevPosition,
                    position
                ]);
                const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff, opacity: 0.5, transparent: true });
                const line = new THREE.Line(lineGeometry, lineMaterial);
                scene.add(line);
                currentObjects.push(line);
            }
        }
        
        updateSceneInfo('Math classes visualization', currentObjects.length);
        updateStatus('Math demo scene created!', 'success');
    } catch (error) {
        updateStatus(`Failed to create math demo: ${error.message}`, 'error');
    }
}

function createVisioWebDemo() {
    clearScene();
    
    try {
        // Create a demo showcasing VisioWeb Essential integration
        const geometry = new THREE.TorusKnotGeometry(1, 0.3, 100, 16);
        const material = new THREE.MeshPhongMaterial({ 
            color: 0x9c27b0,
            shininess: 100,
            transparent: true,
            opacity: 0.8
        });
        const torusKnot = new THREE.Mesh(geometry, material);
        
        // Use our Matrix4 class for transformation
        const transform = new Matrix4();
        transform.makeRotationY(Math.PI / 4);
        torusKnot.applyMatrix4(transform);
        
        torusKnot.userData.animate = true;
        torusKnot.castShadow = true;
        
        scene.add(torusKnot);
        currentObjects.push(torusKnot);
        
        // Add particle system
        const particleCount = 1000;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount * 3; i += 3) {
            // Use our Vector3 class for particle positioning
            const pos = new Vector3(
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10
            );
            positions[i] = pos.x;
            positions[i + 1] = pos.y;
            positions[i + 2] = pos.z;
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        const particleMaterial = new THREE.PointsMaterial({ 
            color: 0x64b5f6, 
            size: 0.05,
            transparent: true,
            opacity: 0.6
        });
        const particleSystem = new THREE.Points(particles, particleMaterial);
        
        scene.add(particleSystem);
        currentObjects.push(particleSystem);
        
        updateSceneInfo('VisioWeb Essential demo with particles', currentObjects.length);
        updateStatus('VisioWeb demo created successfully!', 'success');
    } catch (error) {
        updateStatus(`Failed to create VisioWeb demo: ${error.message}`, 'error');
    }
}

function createInteractiveScene() {
    clearScene();
    
    try {
        // Create an interactive scene with multiple geometric shapes
        const shapes = [
            { geometry: new THREE.BoxGeometry(1, 1, 1), color: 0xff6b6b },
            { geometry: new THREE.SphereGeometry(0.7, 16, 16), color: 0x4ecdc4 },
            { geometry: new THREE.ConeGeometry(0.7, 1.5, 8), color: 0x45b7d1 },
            { geometry: new THREE.CylinderGeometry(0.5, 0.5, 1.5, 8), color: 0xf9ca24 },
            { geometry: new THREE.OctahedronGeometry(0.8), color: 0xf0932b }
        ];
        
        shapes.forEach((shape, index) => {
            const material = new THREE.MeshPhongMaterial({ 
                color: shape.color,
                shininess: 100 
            });
            const mesh = new THREE.Mesh(shape.geometry, material);
            
            // Use our Vector3 and Quaternion classes for positioning and rotation
            const position = new Vector3(
                (index - 2) * 2.5,
                Math.sin(index) * 2,
                0
            );
            mesh.position.copy(position);
            
            const rotation = new Quaternion();
            rotation.setFromAxisAngle(new Vector3(1, 1, 0).normalize(), index * Math.PI / 4);
            mesh.quaternion.copy(rotation);
            
            mesh.userData.animate = true;
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            
            scene.add(mesh);
            currentObjects.push(mesh);
        });
        
        updateSceneInfo('Interactive geometric shapes', currentObjects.length);
        updateStatus('Interactive scene created!', 'success');
    } catch (error) {
        updateStatus(`Failed to create interactive scene: ${error.message}`, 'error');
    }
}

// Performance Testing Functions
function runPerformanceTest() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        const iterations = 100000;
        const startTime = performance.now();
        
        // Test Vector3 operations performance
        for (let i = 0; i < iterations; i++) {
            const v1 = new Vector3(Math.random(), Math.random(), Math.random());
            const v2 = new Vector3(Math.random(), Math.random(), Math.random());
            v1.add(v2).normalize().cross(v2);
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        const opsPerSecond = (iterations / duration * 1000).toFixed(0);
        
        output.innerHTML = `
            <strong>Performance Test Results:</strong><br>
            Iterations: ${iterations.toLocaleString()}<br>
            Duration: ${duration.toFixed(2)}ms<br>
            Operations/second: ${Number(opsPerSecond).toLocaleString()}<br>
            <span style="color: #4caf50;">✓ Performance test completed!</span>
        `;
        
        updateStatus('Performance test completed!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Performance test failed: ${error.message}</span>`;
        updateStatus('Performance test failed', 'error');
    }
}

function runMemoryTest() {
    const output = document.getElementById('math-output');
    output.style.display = 'block';
    
    try {
        const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 'N/A';
        const vectors = [];
        
        // Create many vector instances
        for (let i = 0; i < 10000; i++) {
            vectors.push(new Vector3(i, i * 2, i * 3));
        }
        
        const afterCreation = performance.memory ? performance.memory.usedJSHeapSize : 'N/A';
        
        // Clear references
        vectors.length = 0;
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
        
        const afterCleanup = performance.memory ? performance.memory.usedJSHeapSize : 'N/A';
        
        output.innerHTML = `
            <strong>Memory Test Results:</strong><br>
            Initial memory: ${typeof initialMemory === 'number' ? (initialMemory / 1024 / 1024).toFixed(2) + ' MB' : initialMemory}<br>
            After creating 10k vectors: ${typeof afterCreation === 'number' ? (afterCreation / 1024 / 1024).toFixed(2) + ' MB' : afterCreation}<br>
            After cleanup: ${typeof afterCleanup === 'number' ? (afterCleanup / 1024 / 1024).toFixed(2) + ' MB' : afterCleanup}<br>
            <span style="color: #4caf50;">✓ Memory test completed!</span>
        `;
        
        updateStatus('Memory test completed!', 'success');
    } catch (error) {
        output.innerHTML = `<span style="color: #f44336;">✗ Memory test failed: ${error.message}</span>`;
        updateStatus('Memory test failed', 'error');
    }
}

// Utility Functions
function clearScene() {
    currentObjects.forEach(obj => {
        scene.remove(obj);
        if (obj.geometry) obj.geometry.dispose();
        if (obj.material) {
            if (Array.isArray(obj.material)) {
                obj.material.forEach(mat => mat.dispose());
            } else {
                obj.material.dispose();
            }
        }
    });
    currentObjects.length = 0;
    updateSceneInfo('Empty scene', 0);
    updateStatus('Scene cleared', 'success');
}

function resetCamera() {
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);
    updateStatus('Camera reset to default position', 'success');
}

function toggleWireframe() {
    wireframeMode = !wireframeMode;
    currentObjects.forEach(obj => {
        if (obj.material && obj.material.wireframe !== undefined) {
            obj.material.wireframe = wireframeMode;
        }
    });
    updateStatus(`Wireframe mode: ${wireframeMode ? 'ON' : 'OFF'}`, 'success');
}
