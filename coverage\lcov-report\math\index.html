
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for math</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> math</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.33% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>254/1314</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.43% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>59/232</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.79% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>51/234</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.19% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>241/1137</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="Matrix4.js"><a href="Matrix4.js.html">Matrix4.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="329" class="abs low">0/329</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="26" class="abs low">0/26</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="215" class="abs low">0/215</td>
	</tr>

<tr>
	<td class="file low" data-value="Quaternion.js"><a href="Quaternion.js.html">Quaternion.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="297" class="abs low">0/297</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="38" class="abs low">0/38</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="274" class="abs low">0/274</td>
	</tr>

<tr>
	<td class="file low" data-value="Vector2.js"><a href="Vector2.js.html">Vector2.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="146" class="abs low">0/146</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="54" class="abs low">0/54</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="143" class="abs low">0/143</td>
	</tr>

<tr>
	<td class="file low" data-value="Vector3.js"><a href="Vector3.js.html">Vector3.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="278" class="abs low">0/278</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="47" class="abs low">0/47</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="72" class="abs low">0/72</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="255" class="abs low">0/255</td>
	</tr>

<tr>
	<td class="file high" data-value="Vector4.js"><a href="Vector4.js.html">Vector4.js</a></td>
	<td data-value="96.21" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.21" class="pct high">96.21%</td>
	<td data-value="264" class="abs high">254/264</td>
	<td data-value="86.76" class="pct high">86.76%</td>
	<td data-value="68" class="abs high">59/68</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="51" class="abs high">51/51</td>
	<td data-value="96.4" class="pct high">96.4%</td>
	<td data-value="250" class="abs high">241/250</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-04T21:45:31.297Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    