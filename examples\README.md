# VisioWeb Essential Examples

This directory contains example implementations demonstrating how to use the refactored VisioWeb Essential library.

## Examples

### 1. Mapviewer Demo (`mapviewer-demo.html`)
A complete implementation showing how to integrate the refactored VisioWeb Essential library with the original VisioWeb mapviewer component.

**Features:**
- Full mapviewer initialization and configuration
- Integration with refactored ES6 modules
- Progress tracking and loading states
- Place and route management
- Navigation system demonstration

**Requirements:**
- VisioWeb core library (`visioweb.js`)
- UIKit framework for UI components
- Lodash for utility functions

### 2. Standalone Demo (`standalone-demo.html`)
A self-contained demonstration that works without external dependencies, using mock data to showcase the refactored library structure.

**Features:**
- Interactive demos for all refactored components
- Content management demonstration
- Venue navigation examples
- Route and navigation system testing
- Full integration workflow
- Test results display

**No external dependencies required** - runs entirely with our refactored modules.

## Running the Examples

### Option 1: Standalone Demo (Recommended for testing)
1. Open `standalone-demo.html` in a modern web browser
2. Click the various demo buttons to see each component in action
3. View the console for detailed logging

### Option 2: Full Mapviewer Demo
1. Ensure you have access to the VisioWeb core library
2. Update the script src paths in `mapviewer-demo.html` to point to your VisioWeb installation
3. Open `mapviewer-demo.html` in a web browser
4. The demo will initialize a real 3D mapviewer with our refactored library

## Integration Patterns

The examples demonstrate several key integration patterns:

### ES6 Module Imports
```javascript
import VisioWebEssential from '../src/essential/index.js';
import ContentManager from '../src/essential/content/ContentManager.js';
```

### Component Initialization
```javascript
const essential = new VisioWebEssential({
    element: document.getElementById('container'),
    imagePath: './media'
});
```

### Event Handling
```javascript
essential.onObjectMouseUp = ({targetElement}) => {
    // Handle map interactions
};
```

### Configuration
```javascript
essential.setParameters({
    parameters: {
        baseURL: 'https://mapserver.visioglobe.com/',
        hash: 'your-map-hash',
        locale: { language: 'en' }
    }
});
```

## Browser Compatibility

The examples are designed to work in modern browsers that support:
- ES6 modules
- Async/await
- Modern JavaScript features

For older browser support, consider using a transpiler like Babel.

## Next Steps

After running the examples:
1. Examine the source code to understand integration patterns
2. Adapt the examples for your specific use case
3. Refer to the main README.md for detailed API documentation
4. Run the test suite to verify functionality: `npm test`
