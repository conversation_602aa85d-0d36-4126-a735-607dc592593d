/**
 * @fileoverview Test utilities and helpers
 */

/**
 * Creates a mock Matrix4 for testing
 */
export function createMockMatrix4(elements) {
  return {
    elements: elements || [
      1, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    ]
  };
}

/**
 * Creates a translation matrix
 */
export function createTranslationMatrix(x, y, z) {
  return createMockMatrix4([
    1, 0, 0, 0,
    0, 1, 0, 0,
    0, 0, 1, 0,
    x, y, z, 1
  ]);
}

/**
 * Creates a scale matrix
 */
export function createScaleMatrix(x, y, z) {
  return createMockMatrix4([
    x, 0, 0, 0,
    0, y, 0, 0,
    0, 0, z, 0,
    0, 0, 0, 1
  ]);
}

/**
 * Creates a mock quaternion for testing
 */
export function createMockQuaternion(x = 0, y = 0, z = 0, w = 1) {
  return { x, y, z, w };
}

/**
 * Creates a mock buffer attribute for testing
 */
export function createMockBufferAttribute(values) {
  return {
    getX: jest.fn((index) => values[index * 4]),
    getY: jest.fn((index) => values[index * 4 + 1]),
    getZ: jest.fn((index) => values[index * 4 + 2]),
    getW: jest.fn((index) => values[index * 4 + 3])
  };
}

/**
 * Checks if two numbers are approximately equal
 */
export function approximately(a, b, tolerance = 1e-10) {
  return Math.abs(a - b) < tolerance;
}

/**
 * Checks if two vectors are approximately equal
 */
export function vectorsApproximatelyEqual(v1, v2, tolerance = 1e-10) {
  return approximately(v1.x, v2.x, tolerance) &&
         approximately(v1.y, v2.y, tolerance) &&
         approximately(v1.z, v2.z, tolerance) &&
         approximately(v1.w, v2.w, tolerance);
}
