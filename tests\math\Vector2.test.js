/**
 * @fileoverview Tests for Vector2 class using Three.js
 */

import { Vector2 } from '../../src/math/Vector2.js';

describe('Vector2 (Three.js Integration)', () => {
    describe('Constructor', () => {
        test('should create vector with default values', () => {
            const v = new Vector2();
            expect(v.x).toBe(0);
            expect(v.y).toBe(0);
            expect(v.isVector2).toBe(true);
        });

        test('should create vector with specified values', () => {
            const v = new Vector2(1, 2);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);
            expect(v.isVector2).toBe(true);
        });
    });

    describe('Width/Height compatibility', () => {
        test('should support width and height getters/setters', () => {
            const v = new Vector2(10, 20);
            
            expect(v.width).toBe(10);
            expect(v.height).toBe(20);
            
            v.width = 30;
            v.height = 40;
            
            expect(v.x).toBe(30);
            expect(v.y).toBe(40);
            expect(v.width).toBe(30);
            expect(v.height).toBe(40);
        });
    });

    describe('Three.js Integration', () => {
        test('should inherit all Three.js Vector2 methods', () => {
            const v = new Vector2(1, 2);
            
            // Test basic Three.js methods
            expect(typeof v.set).toBe('function');
            expect(typeof v.add).toBe('function');
            expect(typeof v.sub).toBe('function');
            expect(typeof v.multiply).toBe('function');
            expect(typeof v.normalize).toBe('function');
            expect(typeof v.length).toBe('function');
            expect(typeof v.dot).toBe('function');
            expect(typeof v.angle).toBe('function');
        });

        test('should work with Three.js Vector2 operations', () => {
            const v1 = new Vector2(1, 2);
            const v2 = new Vector2(3, 4);
            
            // Test addition
            const result = v1.clone().add(v2);
            expect(result.x).toBe(4);
            expect(result.y).toBe(6);
            expect(result).toBeInstanceOf(Vector2);
        });

        test('should calculate length correctly', () => {
            const v = new Vector2(3, 4);
            expect(v.length()).toBe(5);
        });

        test('should normalize correctly', () => {
            const v = new Vector2(3, 4);
            v.normalize();
            expect(v.length()).toBeCloseTo(1, 5);
        });

        test('should calculate dot product correctly', () => {
            const v1 = new Vector2(1, 2);
            const v2 = new Vector2(3, 4);
            expect(v1.dot(v2)).toBe(11); // 1*3 + 2*4 = 11
        });

        test('should calculate angle correctly', () => {
            const v = new Vector2(1, 0);
            expect(v.angle()).toBeCloseTo(0, 5);
        });
    });

    describe('Custom clone method', () => {
        test('should return our Vector2 class instance', () => {
            const v1 = new Vector2(1, 2);
            const v2 = v1.clone();
            
            expect(v2).toBeInstanceOf(Vector2);
            expect(v2.isVector2).toBe(true);
            expect(v2.x).toBe(1);
            expect(v2.y).toBe(2);
            expect(v2).not.toBe(v1); // Should be different instances
        });
    });

    describe('Compatibility with existing code', () => {
        test('should work with array operations', () => {
            const v = new Vector2();
            v.fromArray([1, 2]);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);

            const array = v.toArray();
            expect(array).toEqual([1, 2]);
        });

        test('should work with component access', () => {
            const v = new Vector2(1, 2);
            expect(v.getComponent(0)).toBe(1);
            expect(v.getComponent(1)).toBe(2);

            v.setComponent(0, 10);
            expect(v.x).toBe(10);
        });

        test('should work with scalar operations', () => {
            const v = new Vector2(2, 4);
            v.multiplyScalar(0.5);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);
        });
    });

    describe('Performance and Memory', () => {
        test('should create many vectors without issues', () => {
            const vectors = [];
            for (let i = 0; i < 1000; i++) {
                vectors.push(new Vector2(i, i * 2));
            }
            expect(vectors.length).toBe(1000);
            expect(vectors[999].x).toBe(999);
        });
    });
});
