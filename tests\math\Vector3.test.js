/**
 * @fileoverview Tests for Vector3 class using Three.js
 */

import { Vector3 } from '../../src/math/Vector3.js';

describe('Vector3 (Three.js Integration)', () => {
    describe('Constructor', () => {
        test('should create vector with default values', () => {
            const v = new Vector3();
            expect(v.x).toBe(0);
            expect(v.y).toBe(0);
            expect(v.z).toBe(0);
            expect(v.isVector3).toBe(true);
        });

        test('should create vector with specified values', () => {
            const v = new Vector3(1, 2, 3);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);
            expect(v.z).toBe(3);
            expect(v.isVector3).toBe(true);
        });
    });

    describe('Three.js Integration', () => {
        test('should inherit all Three.js Vector3 methods', () => {
            const v = new Vector3(1, 2, 3);
            
            // Test basic Three.js methods
            expect(typeof v.set).toBe('function');
            expect(typeof v.add).toBe('function');
            expect(typeof v.sub).toBe('function');
            expect(typeof v.multiply).toBe('function');
            expect(typeof v.normalize).toBe('function');
            expect(typeof v.length).toBe('function');
            expect(typeof v.dot).toBe('function');
            expect(typeof v.cross).toBe('function');
        });

        test('should work with Three.js Vector3 operations', () => {
            const v1 = new Vector3(1, 2, 3);
            const v2 = new Vector3(4, 5, 6);
            
            // Test addition
            const result = v1.clone().add(v2);
            expect(result.x).toBe(5);
            expect(result.y).toBe(7);
            expect(result.z).toBe(9);
            expect(result).toBeInstanceOf(Vector3);
        });

        test('should calculate length correctly', () => {
            const v = new Vector3(3, 4, 0);
            expect(v.length()).toBe(5);
        });

        test('should normalize correctly', () => {
            const v = new Vector3(3, 4, 0);
            v.normalize();
            expect(v.length()).toBeCloseTo(1, 5);
        });

        test('should calculate dot product correctly', () => {
            const v1 = new Vector3(1, 2, 3);
            const v2 = new Vector3(4, 5, 6);
            expect(v1.dot(v2)).toBe(32); // 1*4 + 2*5 + 3*6 = 32
        });

        test('should calculate cross product correctly', () => {
            const v1 = new Vector3(1, 0, 0);
            const v2 = new Vector3(0, 1, 0);
            const result = v1.clone().cross(v2);
            expect(result.x).toBe(0);
            expect(result.y).toBe(0);
            expect(result.z).toBe(1);
        });
    });

    describe('Custom clone method', () => {
        test('should return our Vector3 class instance', () => {
            const v1 = new Vector3(1, 2, 3);
            const v2 = v1.clone();
            
            expect(v2).toBeInstanceOf(Vector3);
            expect(v2.isVector3).toBe(true);
            expect(v2.x).toBe(1);
            expect(v2.y).toBe(2);
            expect(v2.z).toBe(3);
            expect(v2).not.toBe(v1); // Should be different instances
        });
    });

    describe('Compatibility with existing code', () => {
        test('should work with array operations', () => {
            const v = new Vector3();
            v.fromArray([1, 2, 3]);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);
            expect(v.z).toBe(3);

            const array = v.toArray();
            expect(array).toEqual([1, 2, 3]);
        });

        test('should work with component access', () => {
            const v = new Vector3(1, 2, 3);
            expect(v.getComponent(0)).toBe(1);
            expect(v.getComponent(1)).toBe(2);
            expect(v.getComponent(2)).toBe(3);

            v.setComponent(0, 10);
            expect(v.x).toBe(10);
        });

        test('should work with scalar operations', () => {
            const v = new Vector3(2, 4, 6);
            v.multiplyScalar(0.5);
            expect(v.x).toBe(1);
            expect(v.y).toBe(2);
            expect(v.z).toBe(3);
        });
    });

    describe('Performance and Memory', () => {
        test('should create many vectors without issues', () => {
            const vectors = [];
            for (let i = 0; i < 1000; i++) {
                vectors.push(new Vector3(i, i * 2, i * 3));
            }
            expect(vectors.length).toBe(1000);
            expect(vectors[999].x).toBe(999);
        });
    });
});
