<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Integration Demo - VisioWeb Essential Library</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            display: flex;
            height: calc(100vh - 120px);
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h3 {
            margin: 0 0 15px 0;
            color: #64b5f6;
            font-size: 1.3em;
        }

        .demo-button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background: linear-gradient(45deg, #42a5f5, #1976d2);
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            background: linear-gradient(45deg, #1976d2, #0d47a1);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        .canvas-container {
            flex: 1;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
        }

        #threejs-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 200px;
        }

        .info-panel h4 {
            margin: 0 0 10px 0;
            color: #64b5f6;
        }

        .info-panel p {
            margin: 5px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .status {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status.success {
            border-color: #4caf50;
            color: #4caf50;
        }

        .status.error {
            border-color: #f44336;
            color: #f44336;
        }

        .math-output {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #64b5f6;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Three.js Integration Demo</h1>
        <p>VisioWeb Essential Library with Three.js Math Classes</p>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="demo-section">
                <h3>🧮 Math Classes</h3>
                <button class="demo-button" onclick="testVector3()">Test Vector3 Integration</button>
                <button class="demo-button" onclick="testVector2()">Test Vector2 Integration</button>
                <button class="demo-button" onclick="testMatrix4()">Test Matrix4 Integration</button>
                <button class="demo-button" onclick="testQuaternion()">Test Quaternion Integration</button>
                <div id="math-output" class="math-output" style="display: none;"></div>
            </div>

            <div class="demo-section">
                <h3>🎨 3D Scenes</h3>
                <button class="demo-button" onclick="createBasicScene()">Basic Rotating Cube</button>
                <button class="demo-button" onclick="createMathDemo()">Math Classes Visualization</button>
                <button class="demo-button" onclick="createVisioWebDemo()">VisioWeb Essential Demo</button>
                <button class="demo-button" onclick="createInteractiveScene()">Interactive Scene</button>
            </div>

            <div class="demo-section">
                <h3>⚡ Performance Tests</h3>
                <button class="demo-button" onclick="runPerformanceTest()">Vector Operations Benchmark</button>
                <button class="demo-button" onclick="runMemoryTest()">Memory Usage Test</button>
            </div>

            <div class="demo-section">
                <h3>🔧 Utilities</h3>
                <button class="demo-button" onclick="clearScene()">Clear Scene</button>
                <button class="demo-button" onclick="resetCamera()">Reset Camera</button>
                <button class="demo-button" onclick="toggleWireframe()">Toggle Wireframe</button>
            </div>
        </div>

        <div class="canvas-container">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Loading Three.js and Math Libraries...</p>
            </div>
            <canvas id="threejs-canvas" style="display: none;"></canvas>
            
            <div class="info-panel" id="info-panel" style="display: none;">
                <h4>Scene Information</h4>
                <p id="scene-info">No scene loaded</p>
                <p id="object-count">Objects: 0</p>
                <p id="fps-counter">FPS: --</p>
            </div>

            <div class="status" id="status">Ready to load demonstrations</div>
        </div>
    </div>

    <!-- Load Three.js from CDN for faster loading -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- Load our math classes as ES6 modules -->
    <script type="module">
        // Wait for Three.js to load
        if (typeof THREE === 'undefined') {
            console.error('Three.js not loaded! Please check CDN links.');
            document.getElementById('status').textContent = 'Error: Three.js failed to load';
            document.getElementById('status').className = 'status error';
            throw new Error('Three.js not loaded');
        }

        // Import our refactored math classes
        let Vector2, Vector3, Matrix4, Quaternion;
        let VisioWebEssential, ContentManager, NavigationManager, RouteManager, VenueManager;

        try {
            const { Vector2: V2 } = await import('./src/math/Vector2.js');
            const { Vector3: V3 } = await import('./src/math/Vector3.js');
            const { Matrix4: M4 } = await import('./src/math/Matrix4.js');
            const { Quaternion: Q } = await import('./src/math/Quaternion.js');

            Vector2 = V2;
            Vector3 = V3;
            Matrix4 = M4;
            Quaternion = Q;

            console.log('✅ Math classes loaded successfully');
        } catch (error) {
            console.error('❌ Failed to load math classes:', error);
            document.getElementById('status').textContent = 'Error loading math classes: ' + error.message;
            document.getElementById('status').className = 'status error';
        }

        // Try to import VisioWeb Essential classes (optional)
        try {
            const vwe = await import('./src/essential/VisioWebEssential.js');
            const cm = await import('./src/essential/content/ContentManager.js');
            const nm = await import('./src/essential/navigation/NavigationManager.js');
            const rm = await import('./src/essential/routing/RouteManager.js');
            const vm = await import('./src/essential/venue/VenueManager.js');

            VisioWebEssential = vwe.VisioWebEssential;
            ContentManager = cm.ContentManager;
            NavigationManager = nm.NavigationManager;
            RouteManager = rm.RouteManager;
            VenueManager = vm.VenueManager;

            console.log('✅ VisioWeb Essential classes loaded successfully');
        } catch (error) {
            console.warn('⚠️ VisioWeb Essential classes not available:', error.message);
            // This is optional, so we continue without them
        }

        // Global variables for the demo
        let scene, camera, renderer, controls, animationId;
        let currentObjects = [];
        let wireframeMode = false;
        let fpsCounter = 0;
        let lastTime = 0;

        // Make classes available globally for button handlers
        window.Vector2 = Vector2;
        window.Vector3 = Vector3;
        window.Matrix4 = Matrix4;
        window.Quaternion = Quaternion;
        window.VisioWebEssential = VisioWebEssential;
        window.ContentManager = ContentManager;
        window.NavigationManager = NavigationManager;
        window.RouteManager = RouteManager;
        window.VenueManager = VenueManager;

        // Initialize the Three.js scene
        function initThreeJS() {
            try {
                const canvas = document.getElementById('threejs-canvas');
                const container = canvas.parentElement;

                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x1a1a2e);

            // Create camera
            camera = new THREE.PerspectiveCamera(
                75, 
                container.clientWidth / container.clientHeight, 
                0.1, 
                1000
            );
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true,
                alpha: true 
            });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // Add lights
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

                // Add orbit controls (check if available)
                if (typeof THREE.OrbitControls !== 'undefined') {
                    controls = new THREE.OrbitControls(camera, renderer.domElement);
                    controls.enableDamping = true;
                    controls.dampingFactor = 0.05;
                    console.log('✅ OrbitControls initialized');
                } else {
                    console.warn('⚠️ OrbitControls not available, using basic camera setup');
                }

                // Handle window resize
                window.addEventListener('resize', onWindowResize);

                // Start render loop
                animate();

                // Hide loading, show canvas and info
                document.getElementById('loading').style.display = 'none';
                canvas.style.display = 'block';
                document.getElementById('info-panel').style.display = 'block';

                updateStatus('Three.js initialized successfully!', 'success');
                updateSceneInfo('Empty scene', 0);
                console.log('✅ Three.js scene initialized successfully');

            } catch (error) {
                console.error('❌ Failed to initialize Three.js scene:', error);
                updateStatus('Error initializing 3D scene: ' + error.message, 'error');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('threejs-canvas').style.display = 'none';
            }
        }

        function onWindowResize() {
            const container = document.getElementById('threejs-canvas').parentElement;
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        }

        function animate(currentTime = 0) {
            animationId = requestAnimationFrame(animate);

            // Calculate FPS
            if (currentTime - lastTime >= 1000) {
                document.getElementById('fps-counter').textContent = `FPS: ${fpsCounter}`;
                fpsCounter = 0;
                lastTime = currentTime;
            }
            fpsCounter++;

            // Update controls if available
            if (controls) {
                controls.update();
            }

            // Animate objects
            currentObjects.forEach(obj => {
                if (obj.userData.animate) {
                    obj.rotation.x += 0.01;
                    obj.rotation.y += 0.01;
                }
            });

            renderer.render(scene, camera);
        }

        function updateStatus(message, type = '') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateSceneInfo(description, objectCount) {
            document.getElementById('scene-info').textContent = description;
            document.getElementById('object-count').textContent = `Objects: ${objectCount}`;
        }

        // Make functions available globally
        window.initThreeJS = initThreeJS;
        window.updateStatus = updateStatus;
        window.updateSceneInfo = updateSceneInfo;
        window.scene = scene;
        window.camera = camera;
        window.renderer = renderer;
        window.currentObjects = currentObjects;
        window.wireframeMode = wireframeMode;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Check if Three.js is loaded
            if (typeof THREE !== 'undefined') {
                initThreeJS();
            } else {
                updateStatus('Error: Three.js failed to load', 'error');
            }
        });
    </script>

    <!-- Demo functions script -->
    <script src="./demo-functions.js"></script>
</body>
</html>
