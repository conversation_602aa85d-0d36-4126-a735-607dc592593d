/**
 * @fileoverview Vector2 - 2D vector class using Three.js
 * <AUTHOR> Authors (refactored for our library)
 */

// Handle both browser (global THREE) and Node.js (ES6 import) environments
let ThreeVector2;
if (typeof window !== 'undefined' && typeof THREE !== 'undefined') {
    // Browser environment with global THREE
    ThreeVector2 = THREE.Vector2;
} else {
    // Node.js environment - import Three.js for testing
    try {
        const THREE_MODULE = require('three');
        ThreeVector2 = THREE_MODULE.Vector2;
    } catch (error) {
        // If Three.js not available in test environment, use ES6 import
        const { Vector2: ThreeVector2Import } = require('three');
        ThreeVector2 = ThreeVector2Import;
    }
}

/**
 * 2D vector class extending Three.js Vector2 with additional compatibility methods
 * Maintains API compatibility with our existing codebase while leveraging Three.js
 *
 * This class inherits all Three.js Vector2 methods including:
 * - Basic operations: set, add, sub, multiply, divide, etc.
 * - Vector math: dot, normalize, length, angle, etc.
 * - Utility methods: clone, copy, equals, toArray, fromArray, etc.
 */
class Vector2 extends ThreeVector2 {
    constructor(x = 0, y = 0) {
        super(x, y);

        // Ensure isVector2 property is set for compatibility
        Object.defineProperty(this, "isVector2", {
            value: true,
            writable: false,
            enumerable: false,
            configurable: false
        });
    }

    /**
     * Override clone to return our Vector2 class instead of Three.js Vector2
     * @returns {Vector2} A new Vector2 instance with the same values
     */
    clone() {
        return new Vector2(this.x, this.y);
    }

    /**
     * Width getter for compatibility (alias for x)
     */
    get width() {
        return this.x;
    }

    /**
     * Width setter for compatibility (alias for x)
     */
    set width(value) {
        this.x = value;
    }

    /**
     * Height getter for compatibility (alias for y)
     */
    get height() {
        return this.y;
    }

    /**
     * Height setter for compatibility (alias for y)
     */
    set height(value) {
        this.y = value;
    }



}

export { Vector2 };
