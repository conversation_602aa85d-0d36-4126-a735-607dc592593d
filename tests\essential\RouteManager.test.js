/**
 * Test suite for RouteManager class
 * 
 * This test suite verifies the functionality of the RouteManager class
 * including route computation, waypoint management, and route visualization.
 */

import { RouteManager } from '../../src/essential/routing/RouteManager.js';

// Mock VisioWeb Essential instance
const createMockEssential = () => ({
  _mapviewer: {
    computeRoute: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    multiBuildingView: {
      goTo: jest.fn().mockResolvedValue()
    }
  },
  _parameters: {
    computeRouteOptions: {
      routingParameters: {},
      navigationParameters: {},
      accessibleRoute: {
        excludedAttributes: ["stairway", "escalator"],
        excludedModalities: []
      }
    },
    mapview: {
      colors: {
        pathPast: "#d7d8d9",
        pathCurrent: "#00c5eb",
        pathFuture: "#0099ae"
      },
      config: {
        viewtype: "multifloor"
      }
    },
    initRouteOptions: {}
  },
  _imagePath: "./images/",
  location: null,
  content: {
    resetActivePlace: jest.fn()
  },
  navigation: {
    _create: jest.fn(),
    _clear: jest.fn()
  }
});

// Mock visioweb Route class
global.visioweb = {
  Route: jest.fn().mockImplementation((mapviewer, data, options) => ({
    isValid: jest.fn().mockReturnValue(true),
    show: jest.fn().mockResolvedValue(),
    remove: jest.fn(),
    hideLinks: jest.fn(),
    showLinks: jest.fn(),
    initialFloor: "floor1",
    getViewpointPosition: jest.fn().mockReturnValue({ x: 0, y: 0, z: 0 }),
    navigation: {},
    getStatistics: jest.fn().mockReturnValue({
      duration: 300,
      distance: 150
    })
  }))
};

describe('RouteManager', () => {
  let routeManager;
  let mockEssential;

  beforeEach(() => {
    mockEssential = createMockEssential();

    // Mock DOM methods before creating RouteManager
    const mockElement = {
      id: '',
      innerHTML: ''
    };

    jest.spyOn(document, 'createElement').mockReturnValue(mockElement);
    jest.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    jest.spyOn(document.head, 'appendChild').mockImplementation(() => {});

    routeManager = new RouteManager(mockEssential);

    // Clear all mocks after creation
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore all mocks
    jest.restoreAllMocks();
  });

  describe('Constructor', () => {
    test('should initialize with correct default values', () => {
      expect(routeManager._essential).toBe(mockEssential);
      expect(routeManager._from).toBeNull();
      expect(routeManager._waypoints).toEqual([]);
      expect(routeManager._to).toBeNull();
      expect(routeManager._currentRoute).toBeNull();
      expect(routeManager._destinationOrder).toBe("optimalFinishOnLast");
      expect(routeManager.accessible).toBe(false);
      expect(routeManager.computeNavigation).toBe(true);
    });

    test('should initialize floor change UI', () => {
      // Create a new RouteManager to test DOM initialization
      const mockElement = {
        id: '',
        innerHTML: ''
      };

      const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockElement);
      const bodyAppendSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => {});
      const headAppendSpy = jest.spyOn(document.head, 'appendChild').mockImplementation(() => {});

      new RouteManager(mockEssential);

      expect(createElementSpy).toHaveBeenCalledWith('div');
      expect(createElementSpy).toHaveBeenCalledWith('style');
      expect(bodyAppendSpy).toHaveBeenCalled();
      expect(headAppendSpy).toHaveBeenCalled();

      createElementSpy.mockRestore();
      bodyAppendSpy.mockRestore();
      headAppendSpy.mockRestore();
    });
  });

  describe('Route Validation', () => {
    test('isValid should return false when no current route', () => {
      expect(routeManager.isValid).toBe(false);
    });

    test('isValid should return true when current route exists', () => {
      routeManager._currentRoute = {};
      expect(routeManager.isValid).toBe(true);
    });
  });

  describe('Setting Route Points', () => {
    test('setFrom should set starting point', () => {
      routeManager.setFrom({ from: "place1" });
      expect(routeManager._from).toBe("place1");
    });

    test('setFrom should throw error if from parameter is missing', () => {
      expect(() => {
        routeManager.setFrom({});
      }).toThrow("expecting {from}");
    });

    test('setFrom should remove from waypoints if it exists there', () => {
      routeManager._waypoints = ["place1", "place2"];
      routeManager.setFrom({ from: "place1" });
      expect(routeManager._waypoints).toEqual(["place2"]);
    });

    test('setFrom should throw error if already set as destination', () => {
      routeManager._to = "place1";
      expect(() => {
        routeManager.setFrom({ from: "place1" });
      }).toThrow("Already set as destination");
    });

    test('setTo should set destination point', () => {
      routeManager.setTo({ to: "place2" });
      expect(routeManager._to).toBe("place2");
    });

    test('setTo should throw error if to parameter is missing', () => {
      expect(() => {
        routeManager.setTo({});
      }).toThrow("expecting {to}");
    });

    test('setTo should throw error if already set as start', () => {
      routeManager._from = "place1";
      expect(() => {
        routeManager.setTo({ to: "place1" });
      }).toThrow("Already set as start");
    });
  });

  describe('Waypoint Management', () => {
    test('addWaypoint should add waypoint', () => {
      routeManager.addWaypoint({ waypoint: "waypoint1" });
      expect(routeManager._waypoints).toContain("waypoint1");
    });

    test('addWaypoint should throw error if waypoint parameter is missing', () => {
      expect(() => {
        routeManager.addWaypoint({});
      }).toThrow("expecting {waypoint}");
    });

    test('addWaypoint should not add duplicate waypoints', () => {
      routeManager._waypoints = ["waypoint1"];
      routeManager.addWaypoint({ waypoint: "waypoint1" });
      expect(routeManager._waypoints).toEqual(["waypoint1"]);
    });

    test('addWaypoint should not add start or destination as waypoint', () => {
      routeManager._from = "start";
      routeManager._to = "destination";
      
      routeManager.addWaypoint({ waypoint: "start" });
      routeManager.addWaypoint({ waypoint: "destination" });
      
      expect(routeManager._waypoints).toEqual([]);
    });

    test('removeWaypoint should remove existing waypoint', () => {
      routeManager._waypoints = ["waypoint1", "waypoint2"];
      routeManager.removeWaypoint({ waypoint: "waypoint1" });
      expect(routeManager._waypoints).toEqual(["waypoint2"]);
    });

    test('getWaypoints should return copy of waypoints array', () => {
      routeManager._waypoints = ["waypoint1", "waypoint2"];
      const waypoints = routeManager.getWaypoints();
      
      expect(waypoints).toEqual(["waypoint1", "waypoint2"]);
      expect(waypoints).not.toBe(routeManager._waypoints); // Should be a copy
    });
  });

  describe('Route Status Checks', () => {
    test('hasFrom should return correct status', () => {
      expect(routeManager.hasFrom()).toBe(false);
      
      routeManager._from = "place1";
      expect(routeManager.hasFrom()).toBe(true);
    });

    test('hasTo should return correct status', () => {
      expect(routeManager.hasTo()).toBe(false);
      
      routeManager._to = "place2";
      expect(routeManager.hasTo()).toBe(true);
    });
  });

  describe('Route Configuration', () => {
    test('setDestinationOrder should set order', () => {
      routeManager.setDestinationOrder({ order: "optimal" });
      expect(routeManager._destinationOrder).toBe("optimal");
    });

    test('setDestinationOrder should throw error if order parameter is missing', () => {
      expect(() => {
        routeManager.setDestinationOrder({});
      }).toThrow("expecting {order}");
    });

    test('setAccessible should set accessibility', () => {
      routeManager.setAccessible({ accessible: true });
      expect(routeManager.accessible).toBe(true);
      
      routeManager.setAccessible({ accessible: false });
      expect(routeManager.accessible).toBe(false);
    });

    test('setComputeNavigation should set navigation computation', () => {
      routeManager.setComputeNavigation({ computeNavigation: false });
      expect(routeManager.computeNavigation).toBe(false);
      
      routeManager.setComputeNavigation({ computeNavigation: true });
      expect(routeManager.computeNavigation).toBe(true);
    });

    test('isAccessible should return accessibility status', () => {
      expect(routeManager.isAccessible()).toBe(false);
      
      routeManager.accessible = true;
      expect(routeManager.isAccessible()).toBe(true);
    });

    test('isNavigationEnabled should return navigation status', () => {
      expect(routeManager.isNavigationEnabled()).toBe(true);
      
      routeManager.computeNavigation = false;
      expect(routeManager.isNavigationEnabled()).toBe(false);
    });
  });

  describe('Route Statistics', () => {
    test('getRouteStatistics should return null when no route', () => {
      expect(routeManager.getRouteStatistics()).toBeNull();
    });

    test('getRouteStatistics should return statistics when route exists', () => {
      const mockRoute = {
        getStatistics: jest.fn().mockReturnValue({
          duration: 300,
          distance: 150
        })
      };
      routeManager._currentRoute = mockRoute;
      
      const stats = routeManager.getRouteStatistics();
      expect(stats).toEqual({ duration: 300, distance: 150 });
    });

    test('getRouteDuration should return duration from statistics', () => {
      const mockRoute = {
        getStatistics: jest.fn().mockReturnValue({
          duration: 300,
          distance: 150
        })
      };
      routeManager._currentRoute = mockRoute;
      
      expect(routeManager.getRouteDuration()).toBe(300);
    });

    test('getRouteDistance should return distance from statistics', () => {
      const mockRoute = {
        getStatistics: jest.fn().mockReturnValue({
          duration: 300,
          distance: 150
        })
      };
      routeManager._currentRoute = mockRoute;
      
      expect(routeManager.getRouteDistance()).toBe(150);
    });
  });

  describe('Route Clearing', () => {
    test('clear should reset all route parameters', () => {
      // Set up route state
      routeManager._from = "start";
      routeManager._to = "destination";
      routeManager._waypoints = ["waypoint1"];
      routeManager._currentRoute = { remove: jest.fn() };
      routeManager._destinationOrder = "optimal";
      
      routeManager.clear();
      
      expect(routeManager._from).toBeNull();
      expect(routeManager._to).toBeNull();
      expect(routeManager._waypoints).toEqual([]);
      expect(routeManager._currentRoute).toBeNull();
      expect(routeManager._destinationOrder).toBe("optimalFinishOnLast");
    });

    test('clear should remove current route if it exists', () => {
      const mockRoute = { remove: jest.fn() };
      routeManager._currentRoute = mockRoute;
      
      routeManager.clear();
      
      expect(mockRoute.remove).toHaveBeenCalled();
      expect(mockEssential.navigation._clear).toHaveBeenCalled();
      expect(mockEssential._mapviewer.off).toHaveBeenCalled();
    });
  });

  describe('Static Constants', () => {
    test('should have correct floor change tags ID', () => {
      expect(RouteManager.FLOOR_CHANGE_TAGS_ID).toBe("VGROUTE-floorChangeTags");
    });

    test('should have floor change CSS defined', () => {
      expect(RouteManager.FLOOR_CHANGE_CSS).toContain(".VGROUTE-floorChange");
      expect(RouteManager.FLOOR_CHANGE_CSS).toContain("@keyframes floorChangeUp");
      expect(RouteManager.FLOOR_CHANGE_CSS).toContain("@keyframes floorChangeDown");
    });
  });
});
