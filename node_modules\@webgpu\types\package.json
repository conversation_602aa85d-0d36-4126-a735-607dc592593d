{"name": "@webgpu/types", "repository": "gpuweb/types", "homepage": "https://github.com/gpuweb/types", "bugs": "https://github.com/gpuweb/types/issues", "version": "0.1.63", "main": "", "types": "dist/index.d.ts", "license": "BSD-3-<PERSON><PERSON>", "files": ["dist/**/*"], "scripts": {"test": "for t in tests/*/ ; do echo \"\\n======== $t ========\" ; (cd \"$t\" && npm i && npm test); done", "build-docs": "cd tsdoc-src && npm ci && npm run build", "generate": "bikeshed-to-ts --in ./gpuweb/spec/index.bs --out ./generated/index.d.ts --forceGlobal --nominal && node fix-generated.mjs && prettier -w generated/index.d.ts", "format": "prettier -w dist/index.d.ts"}, "devDependencies": {"bikeshed-to-ts": "file:third_party/bikeshed-to-ts", "prettier": "^2.2.1"}}