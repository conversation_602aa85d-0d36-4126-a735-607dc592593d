/**
 * VisioWeb Essential Demo - Using Refactored Library
 * This demonstrates how to use our newly organized VisioWeb Essential library
 */

// Import our refactored VisioWeb Essential library
import VisioWebEssential from '../../src/essential/index.js';

// Global variables
let essential;
let currentPlace = null;

// Demo configuration
const DEMO_CONFIG = {
    // Use a demo map - you can replace this with your own map hash
    baseURL: 'https://mapserver.visioglobe.com/',
    hash: 'kd9426d8cb3f1c532f22b5bcbd325c280bd351feb', // Demo island map
    imagePath: './media', // Path to icons and images
    locale: {
        language: navigator.language || 'en'
    }
};

/**
 * Initialize the application when the page loads
 */
window.addEventListener('load', initializeDemo);
window.addEventListener('beforeunload', cleanup);

/**
 * Main initialization function
 */
async function initializeDemo() {
    try {
        updateStatus('libraryStatus', 'Initializing VisioWeb Essential...', 'status');
        
        // Check if VisioWeb core library is available
        if (typeof visioweb === 'undefined') {
            throw new Error('VisioWeb core library not found. Please include visioweb.js');
        }
        
        // Initialize our refactored VisioWeb Essential library
        essential = new VisioWebEssential({
            element: document.getElementById('container'),
            imagePath: DEMO_CONFIG.imagePath
        });
        
        updateStatus('libraryStatus', 'VisioWeb Essential initialized ✓', 'status');
        
        // Set up progress tracking
        setupProgressTracking();
        
        // Configure the map parameters
        essential.setParameters({
            parameters: {
                baseURL: DEMO_CONFIG.baseURL,
                hash: DEMO_CONFIG.hash,
                locale: DEMO_CONFIG.locale
            }
        });
        
        // Create the mapviewer
        updateStatus('mapviewerStatus', 'Creating mapviewer...', 'status');
        await essential.createMapviewer();
        
        updateStatus('mapviewerStatus', 'Mapviewer created ✓', 'status');
        hideProgressOverlay();
        
        // Set up the demo interface
        setupDemoInterface();
        setupEventHandlers();
        
        // Log version information
        console.log('%c VisioWeb Essential Demo %c Refactored Library %c',
                    'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff; font-weight: bold;',
                    'background:#00c5eb ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff',
                    'background:transparent');
        
    } catch (error) {
        console.error('Failed to initialize demo:', error);
        updateStatus('libraryStatus', `Error: ${error.message}`, 'error');
        hideProgressOverlay();
    }
}

/**
 * Set up progress tracking for map loading
 */
function setupProgressTracking() {
    essential.onLoadProgress = (progress) => {
        const percentage = Math.round(progress * 100);
        document.getElementById('progressFill').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent = `${percentage}%`;
        
        if (progress >= 1) {
            setTimeout(hideProgressOverlay, 500);
        }
    };
}

/**
 * Hide the progress overlay
 */
function hideProgressOverlay() {
    const overlay = document.getElementById('progressOverlay');
    overlay.style.display = 'none';
}

/**
 * Set up the demo interface based on venue layout
 */
function setupDemoInterface() {
    const venueLayout = essential.venue.layout;
    
    // Update venue info
    const venueInfo = document.getElementById('venueInfo');
    venueInfo.innerHTML = `
        <strong>Venue:</strong> ${venueLayout.buildings.length} building(s)<br>
        <strong>Floors:</strong> ${venueLayout.buildings.reduce((total, building) => total + building.floors.length, 0)} total
    `;
    
    // Populate building selector
    const buildingSelect = document.getElementById('buildingSelect');
    venueLayout.buildings.forEach(building => {
        const option = document.createElement('option');
        option.value = building.id;
        option.textContent = essential.venue.getLocalizedName({id: building.id});
        buildingSelect.appendChild(option);
    });
    
    // Set default building
    if (venueLayout.buildings.length > 0) {
        const defaultBuilding = venueLayout.buildings[venueLayout.defaultBuildingIndex];
        buildingSelect.value = defaultBuilding.id;
        populateFloorSelector(defaultBuilding.id);
    }
}

/**
 * Populate floor selector based on selected building
 */
function populateFloorSelector(buildingId) {
    const floorSelect = document.getElementById('floorSelect');
    floorSelect.innerHTML = '<option value="">Select Floor</option>';
    
    const building = essential.venue.layout.buildingByID[buildingId];
    if (building) {
        building.floors.forEach(floor => {
            const option = document.createElement('option');
            option.value = floor.id;
            option.textContent = essential.venue.getLocalizedName({id: floor.id});
            floorSelect.appendChild(option);
        });
        
        // Set default floor
        if (building.floors.length > 0) {
            const defaultFloor = building.floors[building.defaultFloorIndex];
            floorSelect.value = defaultFloor.id;
        }
    }
}

/**
 * Set up event handlers for the demo interface
 */
function setupEventHandlers() {
    // Global view button
    document.getElementById('globalBtn').addEventListener('click', () => {
        essential.venue.goToGlobal();
    });
    
    // Building selector
    document.getElementById('buildingSelect').addEventListener('change', (e) => {
        if (e.target.value) {
            essential.venue.goToBuilding({id: e.target.value});
            populateFloorSelector(e.target.value);
        }
    });
    
    // Floor selector
    document.getElementById('floorSelect').addEventListener('change', (e) => {
        if (e.target.value) {
            essential.venue.goToFloor({id: e.target.value});
        }
    });
    
    // Create sample place
    document.getElementById('createPlaceBtn').addEventListener('click', createSamplePlace);
    
    // Route buttons
    document.getElementById('routeBtn').addEventListener('click', createSampleRoute);
    document.getElementById('clearRouteBtn').addEventListener('click', clearRoute);
    
    // Place bubble buttons
    document.getElementById('setFromBtn').addEventListener('click', () => setRoutePoint('from'));
    document.getElementById('setToBtn').addEventListener('click', () => setRoutePoint('to'));
    document.getElementById('closeBubbleBtn').addEventListener('click', hidePlaceBubble);
    
    // Navigation buttons
    document.getElementById('prevInstructionBtn').addEventListener('click', () => {
        essential.navigation.goToPreviousInstruction();
        updateNavigationDisplay();
    });
    
    document.getElementById('nextInstructionBtn').addEventListener('click', () => {
        essential.navigation.goToNextInstruction();
        updateNavigationDisplay();
    });
    
    // Map interaction
    essential.onObjectMouseUp = ({targetElement}) => {
        if (targetElement) {
            showPlaceBubble(targetElement);
        }
    };
    
    // Route computation event
    essential.on('navigationComputed', () => {
        showNavigationPanel();
        updateNavigationDisplay();
    });
}

/**
 * Create a sample place for demonstration
 */
function createSamplePlace() {
    const placeId = `demo_place_${Date.now()}`;
    
    // Create a category for demo places
    essential.content.createCategory('demo_category', {
        name: 'Demo Places',
        color: '#ff6b6b',
        icon: null
    });
    
    // Create the place
    essential.content.createPlace(placeId, {
        name: `Demo Place ${new Date().toLocaleTimeString()}`,
        description: 'This is a sample place created by the demo application.',
        floor: document.getElementById('floorSelect').value || 'default'
    });
    
    // Assign to category
    essential.content.setPlaceContent(placeId, 'demo_category');
    
    updateStatus('libraryStatus', `Created place: ${placeId}`, 'status');
}

/**
 * Show place bubble with place information
 */
function showPlaceBubble(placeId) {
    currentPlace = placeId;
    const place = essential.content.places[placeId];
    
    if (place) {
        document.getElementById('placeTitle').textContent = place.name || placeId;
        document.getElementById('placeDescription').textContent = place.description || 'No description available';
    } else {
        document.getElementById('placeTitle').textContent = placeId;
        document.getElementById('placeDescription').textContent = 'Place information not available';
    }
    
    document.getElementById('placeBubble').style.display = 'block';
    essential.content.setActivePlace({place: placeId});
}

/**
 * Hide the place bubble
 */
function hidePlaceBubble() {
    document.getElementById('placeBubble').style.display = 'none';
    essential.content.resetActivePlace();
    currentPlace = null;
}

/**
 * Set route point (from or to)
 */
function setRoutePoint(type) {
    if (!currentPlace) return;
    
    try {
        if (type === 'from') {
            essential.routing.setFrom(currentPlace);
            updateStatus('libraryStatus', `Route start set to: ${currentPlace}`, 'status');
        } else {
            essential.routing.setTo(currentPlace);
            updateStatus('libraryStatus', `Route destination set to: ${currentPlace}`, 'status');
        }
        
        // Enable route button if both points are set
        const hasFrom = essential.routing.hasFrom();
        const hasTo = essential.routing.hasTo();
        document.getElementById('routeBtn').disabled = !(hasFrom && hasTo);
        
        hidePlaceBubble();
    } catch (error) {
        updateStatus('libraryStatus', `Error setting route point: ${error.message}`, 'error');
    }
}

/**
 * Create a sample route between two points
 */
function createSampleRoute() {
    try {
        // The route computation will trigger the 'navigationComputed' event
        updateStatus('libraryStatus', 'Computing route...', 'status');
    } catch (error) {
        updateStatus('libraryStatus', `Error creating route: ${error.message}`, 'error');
    }
}

/**
 * Clear the current route
 */
function clearRoute() {
    essential.routing.clear();
    document.getElementById('routeBtn').disabled = true;
    document.getElementById('clearRouteBtn').disabled = true;
    document.getElementById('navigationPanel').style.display = 'none';
    updateStatus('libraryStatus', 'Route cleared', 'status');
}

/**
 * Show the navigation panel
 */
function showNavigationPanel() {
    document.getElementById('navigationPanel').style.display = 'block';
    document.getElementById('clearRouteBtn').disabled = false;
}

/**
 * Update navigation display
 */
function updateNavigationDisplay() {
    const instructionText = document.getElementById('instructionText');
    
    if (essential.navigation.nbInstructions > 0) {
        const brief = essential.navigation.getCurrentInstructionBrief();
        const detail = essential.navigation.getCurrentInstructionDetail();
        instructionText.innerHTML = `<strong>${brief}</strong><br>${detail}`;
        
        // Update button states
        document.getElementById('prevInstructionBtn').disabled = 
            essential.navigation.currentInstructionIndex === 0;
        document.getElementById('nextInstructionBtn').disabled = 
            essential.navigation.currentInstructionIndex === essential.navigation.nbInstructions - 1;
    }
}

/**
 * Update status display
 */
function updateStatus(elementId, message, type = '') {
    const element = document.getElementById(elementId);
    element.textContent = message;
    element.className = type;
}

/**
 * Cleanup when page unloads
 */
function cleanup() {
    if (essential) {
        essential.destroyMapviewer();
    }
}
