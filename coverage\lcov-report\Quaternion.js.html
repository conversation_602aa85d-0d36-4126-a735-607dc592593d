
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Quaternion.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> Quaternion.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/297</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/64</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/38</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/274</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @fileoverview Quaternion - Quaternion class for 3D rotations
 * <AUTHOR> Authors
 */
&nbsp;
/**
 * Quaternion class for representing rotations in 3D space
 */
class Quaternion {
<span class="fstat-no" title="function not covered" >    co</span>nstructor(x = <span class="branch-0 cbranch-no" title="branch not covered" >0,</span> y = <span class="branch-0 cbranch-no" title="branch not covered" >0,</span> z = <span class="branch-0 cbranch-no" title="branch not covered" >0,</span> w = <span class="branch-0 cbranch-no" title="branch not covered" >1)</span> {
<span class="cstat-no" title="statement not covered" >        Object.defineProperty(this, "isQuaternion", {</span>
            value: true
        });
<span class="cstat-no" title="statement not covered" >        this._x = x;</span>
<span class="cstat-no" title="statement not covered" >        this._y = y;</span>
<span class="cstat-no" title="statement not covered" >        this._z = z;</span>
<span class="cstat-no" title="statement not covered" >        this._w = w;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    st</span>atic slerp(qa, qb, qm, t) {
<span class="cstat-no" title="statement not covered" >        return qm.copy(qa).slerp(qb, t);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    st</span>atic slerpFlat(dst, dstOffset, src0, srcOffset0, src1, srcOffset1, t) {
        // fuzz-free, array-based Quaternion SLERP operation
        let x0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 0],</span>
            y0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 1],</span>
            z0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 2],</span>
            w0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 3];</span>
        const x1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 0],</span>
            y1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 1],</span>
            z1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 2],</span>
            w1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 3];</span>
<span class="cstat-no" title="statement not covered" >        if (w0 !== w1 || x0 !== x1 || y0 !== y1 || z0 !== z1) {</span>
            let s = <span class="cstat-no" title="statement not covered" >1 - t;</span>
            const cos = <span class="cstat-no" title="statement not covered" >x0 * x1 + y0 * y1 + z0 * z1 + w0 * w1,</span>
                dir = (<span class="cstat-no" title="statement not covered" >cos &gt;= 0 ? 1 : -1)</span>,
                sqrSin = <span class="cstat-no" title="statement not covered" >1 - cos * cos;</span>
            // Skip the Slerp for tiny steps to avoid numeric problems:
<span class="cstat-no" title="statement not covered" >            if (sqrSin &gt; Number.EPSILON) {</span>
                const sin = <span class="cstat-no" title="statement not covered" >Math.sqrt(sqrSin),</span>
                    len = <span class="cstat-no" title="statement not covered" >Math.atan2(sin, cos * dir);</span>
<span class="cstat-no" title="statement not covered" >                s = Math.sin(s * len) / sin;</span>
<span class="cstat-no" title="statement not covered" >                t = Math.sin(t * len) / sin;</span>
            }
            const tDir = <span class="cstat-no" title="statement not covered" >t * dir;</span>
<span class="cstat-no" title="statement not covered" >            x0 = x0 * s + x1 * tDir;</span>
<span class="cstat-no" title="statement not covered" >            y0 = y0 * s + y1 * tDir;</span>
<span class="cstat-no" title="statement not covered" >            z0 = z0 * s + z1 * tDir;</span>
<span class="cstat-no" title="statement not covered" >            w0 = w0 * s + w1 * tDir;</span>
            // Normalize in case we just did a lerp:
<span class="cstat-no" title="statement not covered" >            if (s === 1 - t) {</span>
                const f = <span class="cstat-no" title="statement not covered" >1 / Math.sqrt(x0 * x0 + y0 * y0 + z0 * z0 + w0 * w0);</span>
<span class="cstat-no" title="statement not covered" >                x0 *= f;</span>
<span class="cstat-no" title="statement not covered" >                y0 *= f;</span>
<span class="cstat-no" title="statement not covered" >                z0 *= f;</span>
<span class="cstat-no" title="statement not covered" >                w0 *= f;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        dst[dstOffset] = x0;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 1] = y0;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 2] = z0;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 3] = w0;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    st</span>atic multiplyQuaternionsFlat(dst, dstOffset, src0, srcOffset0, src1, srcOffset1) {
        const x0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0];</span>
        const y0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 1];</span>
        const z0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 2];</span>
        const w0 = <span class="cstat-no" title="statement not covered" >src0[srcOffset0 + 3];</span>
        const x1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1];</span>
        const y1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 1];</span>
        const z1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 2];</span>
        const w1 = <span class="cstat-no" title="statement not covered" >src1[srcOffset1 + 3];</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset] = x0 * w1 + w0 * x1 + y0 * z1 - z0 * y1;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 1] = y0 * w1 + w0 * y1 + z0 * x1 - x0 * z1;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 2] = z0 * w1 + w0 * z1 + x0 * y1 - y0 * x1;</span>
<span class="cstat-no" title="statement not covered" >        dst[dstOffset + 3] = w0 * w1 - x0 * x1 - y0 * y1 - z0 * z1;</span>
<span class="cstat-no" title="statement not covered" >        return dst;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t x() {
<span class="cstat-no" title="statement not covered" >        return this._x;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t x(value) {
<span class="cstat-no" title="statement not covered" >        this._x = value;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t y() {
<span class="cstat-no" title="statement not covered" >        return this._y;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t y(value) {
<span class="cstat-no" title="statement not covered" >        this._y = value;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t z() {
<span class="cstat-no" title="statement not covered" >        return this._z;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t z(value) {
<span class="cstat-no" title="statement not covered" >        this._z = value;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ge</span>t w() {
<span class="cstat-no" title="statement not covered" >        return this._w;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t w(value) {
<span class="cstat-no" title="statement not covered" >        this._w = value;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>t(x, y, z, w) {
<span class="cstat-no" title="statement not covered" >        this._x = x;</span>
<span class="cstat-no" title="statement not covered" >        this._y = y;</span>
<span class="cstat-no" title="statement not covered" >        this._z = z;</span>
<span class="cstat-no" title="statement not covered" >        this._w = w;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    cl</span>one() {
<span class="cstat-no" title="statement not covered" >        return new this.constructor(this._x, this._y, this._z, this._w);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    co</span>py(quaternion) {
<span class="cstat-no" title="statement not covered" >        this._x = quaternion.x;</span>
<span class="cstat-no" title="statement not covered" >        this._y = quaternion.y;</span>
<span class="cstat-no" title="statement not covered" >        this._z = quaternion.z;</span>
<span class="cstat-no" title="statement not covered" >        this._w = quaternion.w;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tFromEuler(euler, update) {
<span class="cstat-no" title="statement not covered" >        if (!(euler &amp;&amp; euler.isEuler)) {</span>
<span class="cstat-no" title="statement not covered" >            throw new Error("THREE.Quaternion: .setFromEuler() now expects an Euler rotation rather than a Vector3 and order.");</span>
        }
        const x = <span class="cstat-no" title="statement not covered" >euler._x,</span> y = <span class="cstat-no" title="statement not covered" >euler._y,</span> z = <span class="cstat-no" title="statement not covered" >euler._z,</span> order = <span class="cstat-no" title="statement not covered" >euler._order;</span>
        // http://www.mathworks.com/matlabcentral/fileexchange/
        // 	20696-function-to-convert-between-dcm-euler-angles-quaternions-and-euler-vectors/
        //	content/SpinCalc.m
        const cos = <span class="cstat-no" title="statement not covered" >Math.cos;</span>
        const sin = <span class="cstat-no" title="statement not covered" >Math.sin;</span>
        const c1 = <span class="cstat-no" title="statement not covered" >cos(x / 2);</span>
        const c2 = <span class="cstat-no" title="statement not covered" >cos(y / 2);</span>
        const c3 = <span class="cstat-no" title="statement not covered" >cos(z / 2);</span>
        const s1 = <span class="cstat-no" title="statement not covered" >sin(x / 2);</span>
        const s2 = <span class="cstat-no" title="statement not covered" >sin(y / 2);</span>
        const s3 = <span class="cstat-no" title="statement not covered" >sin(z / 2);</span>
<span class="cstat-no" title="statement not covered" >        switch (order) {</span>
            case 'XYZ':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'YXZ':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'ZXY':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'ZYX':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'YZX':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'XZY':
<span class="cstat-no" title="statement not covered" >                this._x = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._y = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                this._z = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >                this._w = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            default:
<span class="cstat-no" title="statement not covered" >                console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: " + order);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (update !== false) <span class="cstat-no" title="statement not covered" >this._onChangeCallback();</span></span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tFromAxisAngle(axis, angle) {
        // http://www.euclideanspace.com/maths/geometry/rotations/conversions/angleToQuaternion/index.htm
        // assumes axis is normalized
        const halfAngle = <span class="cstat-no" title="statement not covered" >angle / 2,</span> s = <span class="cstat-no" title="statement not covered" >Math.sin(halfAngle);</span>
<span class="cstat-no" title="statement not covered" >        this._x = axis.x * s;</span>
<span class="cstat-no" title="statement not covered" >        this._y = axis.y * s;</span>
<span class="cstat-no" title="statement not covered" >        this._z = axis.z * s;</span>
<span class="cstat-no" title="statement not covered" >        this._w = Math.cos(halfAngle);</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tFromRotationMatrix(m) {
        // http://www.euclideanspace.com/maths/geometry/rotations/conversions/matrixToQuaternion/index.htm
        // assumes the upper 3x3 of m is a pure rotation matrix (i.e, unscaled)
        const te = <span class="cstat-no" title="statement not covered" >m.elements,</span>
            m11 = <span class="cstat-no" title="statement not covered" >te[0],</span> m12 = <span class="cstat-no" title="statement not covered" >te[4],</span> m13 = <span class="cstat-no" title="statement not covered" >te[8],</span>
            m21 = <span class="cstat-no" title="statement not covered" >te[1],</span> m22 = <span class="cstat-no" title="statement not covered" >te[5],</span> m23 = <span class="cstat-no" title="statement not covered" >te[9],</span>
            m31 = <span class="cstat-no" title="statement not covered" >te[2],</span> m32 = <span class="cstat-no" title="statement not covered" >te[6],</span> m33 = <span class="cstat-no" title="statement not covered" >te[10],</span>
            trace = <span class="cstat-no" title="statement not covered" >m11 + m22 + m33;</span>
<span class="cstat-no" title="statement not covered" >        if (trace &gt; 0) {</span>
            const s = <span class="cstat-no" title="statement not covered" >0.5 / Math.sqrt(trace + 1.0);</span>
<span class="cstat-no" title="statement not covered" >            this._w = 0.25 / s;</span>
<span class="cstat-no" title="statement not covered" >            this._x = (m32 - m23) * s;</span>
<span class="cstat-no" title="statement not covered" >            this._y = (m13 - m31) * s;</span>
<span class="cstat-no" title="statement not covered" >            this._z = (m21 - m12) * s;</span>
        } else <span class="cstat-no" title="statement not covered" >if (m11 &gt; m22 &amp;&amp; m11 &gt; m33) {</span>
            const s = <span class="cstat-no" title="statement not covered" >2.0 * Math.sqrt(1.0 + m11 - m22 - m33);</span>
<span class="cstat-no" title="statement not covered" >            this._w = (m32 - m23) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._x = 0.25 * s;</span>
<span class="cstat-no" title="statement not covered" >            this._y = (m12 + m21) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._z = (m13 + m31) / s;</span>
        } else <span class="cstat-no" title="statement not covered" >if (m22 &gt; m33) {</span>
            const s = <span class="cstat-no" title="statement not covered" >2.0 * Math.sqrt(1.0 + m22 - m11 - m33);</span>
<span class="cstat-no" title="statement not covered" >            this._w = (m13 - m31) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._x = (m12 + m21) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._y = 0.25 * s;</span>
<span class="cstat-no" title="statement not covered" >            this._z = (m23 + m32) / s;</span>
        } else {
            const s = <span class="cstat-no" title="statement not covered" >2.0 * Math.sqrt(1.0 + m33 - m11 - m22);</span>
<span class="cstat-no" title="statement not covered" >            this._w = (m21 - m12) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._x = (m13 + m31) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._y = (m23 + m32) / s;</span>
<span class="cstat-no" title="statement not covered" >            this._z = 0.25 * s;</span>
        }
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    se</span>tFromUnitVectors(vFrom, vTo) {
        // assumes direction vectors vFrom and vTo are normalized
        let r = <span class="cstat-no" title="statement not covered" >vFrom.dot(vTo) + 1;</span>
<span class="cstat-no" title="statement not covered" >        if (r &lt; Number.EPSILON) {</span>
            // vFrom and vTo point in opposite directions
<span class="cstat-no" title="statement not covered" >            r = 0;</span>
<span class="cstat-no" title="statement not covered" >            if (Math.abs(vFrom.x) &gt; Math.abs(vFrom.z)) {</span>
<span class="cstat-no" title="statement not covered" >                this._x = -vFrom.y;</span>
<span class="cstat-no" title="statement not covered" >                this._y = vFrom.x;</span>
<span class="cstat-no" title="statement not covered" >                this._z = 0;</span>
<span class="cstat-no" title="statement not covered" >                this._w = r;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                this._x = 0;</span>
<span class="cstat-no" title="statement not covered" >                this._y = -vFrom.z;</span>
<span class="cstat-no" title="statement not covered" >                this._z = vFrom.y;</span>
<span class="cstat-no" title="statement not covered" >                this._w = r;</span>
            }
        } else {
            // crossVectors( vFrom, vTo ); // inlined to avoid cyclic dependency on Vector3
<span class="cstat-no" title="statement not covered" >            this._x = vFrom.y * vTo.z - vFrom.z * vTo.y;</span>
<span class="cstat-no" title="statement not covered" >            this._y = vFrom.z * vTo.x - vFrom.x * vTo.z;</span>
<span class="cstat-no" title="statement not covered" >            this._z = vFrom.x * vTo.y - vFrom.y * vTo.x;</span>
<span class="cstat-no" title="statement not covered" >            this._w = r;</span>
        }
<span class="cstat-no" title="statement not covered" >        return this.normalize();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    an</span>gleTo(q) {
<span class="cstat-no" title="statement not covered" >        return 2 * Math.acos(Math.abs(Math.max(-1, Math.min(1, this.dot(q)))));</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    ro</span>tateTowards(q, step) {
        const angle = <span class="cstat-no" title="statement not covered" >this.angleTo(q);</span>
<span class="cstat-no" title="statement not covered" >        if (angle === 0) <span class="cstat-no" title="statement not covered" >return this;</span></span>
        const t = <span class="cstat-no" title="statement not covered" >Math.min(1, step / angle);</span>
<span class="cstat-no" title="statement not covered" >        this.slerp(q, t);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    id</span>entity() {
<span class="cstat-no" title="statement not covered" >        return this.set(0, 0, 0, 1);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    in</span>vert() {
        // quaternion is assumed to have unit length
<span class="cstat-no" title="statement not covered" >        return this.conjugate();</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    co</span>njugate() {
<span class="cstat-no" title="statement not covered" >        this._x *= -1;</span>
<span class="cstat-no" title="statement not covered" >        this._y *= -1;</span>
<span class="cstat-no" title="statement not covered" >        this._z *= -1;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    do</span>t(v) {
<span class="cstat-no" title="statement not covered" >        return this._x * v._x + this._y * v._y + this._z * v._z + this._w * v._w;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>ngthSq() {
<span class="cstat-no" title="statement not covered" >        return this._x * this._x + this._y * this._y + this._z * this._z + this._w * this._w;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    le</span>ngth() {
<span class="cstat-no" title="statement not covered" >        return Math.sqrt(this._x * this._x + this._y * this._y + this._z * this._z + this._w * this._w);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    no</span>rmalize() {
        let l = <span class="cstat-no" title="statement not covered" >this.length();</span>
<span class="cstat-no" title="statement not covered" >        if (l === 0) {</span>
<span class="cstat-no" title="statement not covered" >            this._x = 0;</span>
<span class="cstat-no" title="statement not covered" >            this._y = 0;</span>
<span class="cstat-no" title="statement not covered" >            this._z = 0;</span>
<span class="cstat-no" title="statement not covered" >            this._w = 1;</span>
        } else {
<span class="cstat-no" title="statement not covered" >            l = 1 / l;</span>
<span class="cstat-no" title="statement not covered" >            this._x = this._x * l;</span>
<span class="cstat-no" title="statement not covered" >            this._y = this._y * l;</span>
<span class="cstat-no" title="statement not covered" >            this._z = this._z * l;</span>
<span class="cstat-no" title="statement not covered" >            this._w = this._w * l;</span>
        }
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiply(q, p) {
<span class="cstat-no" title="statement not covered" >        if (p !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn("THREE.Quaternion: .multiply() now only accepts one argument. Use .multiplyQuaternions( a, b ) instead.");</span>
<span class="cstat-no" title="statement not covered" >            return this.multiplyQuaternions(q, p);</span>
        }
<span class="cstat-no" title="statement not covered" >        return this.multiplyQuaternions(this, q);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    pr</span>emultiply(q) {
<span class="cstat-no" title="statement not covered" >        return this.multiplyQuaternions(q, this);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    mu</span>ltiplyQuaternions(a, b) {
        // from http://www.euclideanspace.com/maths/algebra/realNormedAlgebra/quaternions/code/index.htm
        const qax = <span class="cstat-no" title="statement not covered" >a._x,</span> qay = <span class="cstat-no" title="statement not covered" >a._y,</span> qaz = <span class="cstat-no" title="statement not covered" >a._z,</span> qaw = <span class="cstat-no" title="statement not covered" >a._w;</span>
        const qbx = <span class="cstat-no" title="statement not covered" >b._x,</span> qby = <span class="cstat-no" title="statement not covered" >b._y,</span> qbz = <span class="cstat-no" title="statement not covered" >b._z,</span> qbw = <span class="cstat-no" title="statement not covered" >b._w;</span>
<span class="cstat-no" title="statement not covered" >        this._x = qax * qbw + qaw * qbx + qay * qbz - qaz * qby;</span>
<span class="cstat-no" title="statement not covered" >        this._y = qay * qbw + qaw * qby + qaz * qbx - qax * qbz;</span>
<span class="cstat-no" title="statement not covered" >        this._z = qaz * qbw + qaw * qbz + qax * qby - qay * qbx;</span>
<span class="cstat-no" title="statement not covered" >        this._w = qaw * qbw - qax * qbx - qay * qby - qaz * qbz;</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    sl</span>erp(qb, t) {
<span class="cstat-no" title="statement not covered" >        if (t === 0) <span class="cstat-no" title="statement not covered" >return this;</span></span>
<span class="cstat-no" title="statement not covered" >        if (t === 1) <span class="cstat-no" title="statement not covered" >return this.copy(qb);</span></span>
        const x = <span class="cstat-no" title="statement not covered" >this._x,</span> y = <span class="cstat-no" title="statement not covered" >this._y,</span> z = <span class="cstat-no" title="statement not covered" >this._z,</span> w = <span class="cstat-no" title="statement not covered" >this._w;</span>
        // http://www.euclideanspace.com/maths/algebra/realNormedAlgebra/quaternions/slerp/
        let cosHalfTheta = <span class="cstat-no" title="statement not covered" >w * qb._w + x * qb._x + y * qb._y + z * qb._z;</span>
<span class="cstat-no" title="statement not covered" >        if (cosHalfTheta &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            this._w = -qb._w;</span>
<span class="cstat-no" title="statement not covered" >            this._x = -qb._x;</span>
<span class="cstat-no" title="statement not covered" >            this._y = -qb._y;</span>
<span class="cstat-no" title="statement not covered" >            this._z = -qb._z;</span>
<span class="cstat-no" title="statement not covered" >            cosHalfTheta = -cosHalfTheta;</span>
        } else {
<span class="cstat-no" title="statement not covered" >            this.copy(qb);</span>
        }
<span class="cstat-no" title="statement not covered" >        if (cosHalfTheta &gt;= 1.0) {</span>
<span class="cstat-no" title="statement not covered" >            this._w = w;</span>
<span class="cstat-no" title="statement not covered" >            this._x = x;</span>
<span class="cstat-no" title="statement not covered" >            this._y = y;</span>
<span class="cstat-no" title="statement not covered" >            this._z = z;</span>
<span class="cstat-no" title="statement not covered" >            return this;</span>
        }
        const sqrSinHalfTheta = <span class="cstat-no" title="statement not covered" >1.0 - cosHalfTheta * cosHalfTheta;</span>
<span class="cstat-no" title="statement not covered" >        if (sqrSinHalfTheta &lt;= Number.EPSILON) {</span>
            const s = <span class="cstat-no" title="statement not covered" >1 - t;</span>
<span class="cstat-no" title="statement not covered" >            this._w = s * w + t * this._w;</span>
<span class="cstat-no" title="statement not covered" >            this._x = s * x + t * this._x;</span>
<span class="cstat-no" title="statement not covered" >            this._y = s * y + t * this._y;</span>
<span class="cstat-no" title="statement not covered" >            this._z = s * z + t * this._z;</span>
<span class="cstat-no" title="statement not covered" >            this.normalize();</span>
<span class="cstat-no" title="statement not covered" >            this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >            return this;</span>
        }
        const sinHalfTheta = <span class="cstat-no" title="statement not covered" >Math.sqrt(sqrSinHalfTheta);</span>
        const halfTheta = <span class="cstat-no" title="statement not covered" >Math.atan2(sinHalfTheta, cosHalfTheta);</span>
        const ratioA = <span class="cstat-no" title="statement not covered" >Math.sin((1 - t) * halfTheta) / sinHalfTheta,</span>
            ratioB = <span class="cstat-no" title="statement not covered" >Math.sin(t * halfTheta) / sinHalfTheta;</span>
<span class="cstat-no" title="statement not covered" >        this._w = (w * ratioA + this._w * ratioB);</span>
<span class="cstat-no" title="statement not covered" >        this._x = (x * ratioA + this._x * ratioB);</span>
<span class="cstat-no" title="statement not covered" >        this._y = (y * ratioA + this._y * ratioB);</span>
<span class="cstat-no" title="statement not covered" >        this._z = (z * ratioA + this._z * ratioB);</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    eq</span>uals(quaternion) {
<span class="cstat-no" title="statement not covered" >        return (quaternion._x === this._x) &amp;&amp; (quaternion._y === this._y) &amp;&amp; (quaternion._z === this._z) &amp;&amp; (quaternion._w === this._w);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    fr</span>omArray(array, offset = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >        this._x = array[offset];</span>
<span class="cstat-no" title="statement not covered" >        this._y = array[offset + 1];</span>
<span class="cstat-no" title="statement not covered" >        this._z = array[offset + 2];</span>
<span class="cstat-no" title="statement not covered" >        this._w = array[offset + 3];</span>
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback();</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    to</span>Array(array = <span class="branch-0 cbranch-no" title="branch not covered" >[],</span> offset = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >        array[offset] = this._x;</span>
<span class="cstat-no" title="statement not covered" >        array[offset + 1] = this._y;</span>
<span class="cstat-no" title="statement not covered" >        array[offset + 2] = this._z;</span>
<span class="cstat-no" title="statement not covered" >        array[offset + 3] = this._w;</span>
<span class="cstat-no" title="statement not covered" >        return array;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    fr</span>omBufferAttribute(attribute, index) {
<span class="cstat-no" title="statement not covered" >        this._x = attribute.getX(index);</span>
<span class="cstat-no" title="statement not covered" >        this._y = attribute.getY(index);</span>
<span class="cstat-no" title="statement not covered" >        this._z = attribute.getZ(index);</span>
<span class="cstat-no" title="statement not covered" >        this._w = attribute.getW(index);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    _o</span>nChange(callback) {
<span class="cstat-no" title="statement not covered" >        this._onChangeCallback = callback;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    _o</span>nChangeCallback() {}
}
&nbsp;
export { Quaternion };
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-04T21:07:49.701Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    