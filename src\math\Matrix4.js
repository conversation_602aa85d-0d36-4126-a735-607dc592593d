/**
 * @fileoverview Matrix4 - 4x4 matrix class using Three.js
 * <AUTHOR> Authors (refactored for our library)
 */

import { Matrix4 as ThreeMatrix4 } from 'three';

/**
 * 4x4 matrix class extending Three.js Matrix4 with additional compatibility methods
 * Maintains API compatibility with our existing codebase while leveraging Three.js
 *
 * This class inherits all Three.js Matrix4 methods including:
 * - Basic operations: set, identity, copy, clone, etc.
 * - Matrix math: multiply, multiplyMatrices, invert, transpose, etc.
 * - Transformations: makeTranslation, makeRotation, makeScale, etc.
 * - Utility methods: equals, toArray, fromArray, etc.
 */
class Matrix4 extends ThreeMatrix4 {
    constructor() {
        super();

        // Ensure isMatrix4 property is set for compatibility
        Object.defineProperty(this, "isMatrix4", {
            value: true,
            writable: false,
            enumerable: false,
            configurable: false
        });
    }

    /**
     * Override clone to return our Matrix4 class instead of Three.js Matrix4
     * @returns {Matrix4} A new Matrix4 instance with the same values
     */
    clone() {
        return new Matrix4().fromArray(this.elements);
    }

}

export { Matrix4 };
