{"name": "3d-graphics-library", "version": "1.0.0", "description": "This project contains a refactored and human-readable version of a heavily minified 3D graphics library, originally contained in a single file `aa.js`.", "main": "src/index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.js$": "babel-jest"}, "testMatch": ["**/tests/**/*.test.js", "**/__tests__/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/**/index.js"]}, "keywords": ["3d", "graphics", "webgl", "three.js", "math", "vector", "matrix"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "babel-jest": "^30.0.4", "jest": "^30.0.4"}}