/**
 * @fileoverview Comprehensive test suite for Vector4 class
 */

import { Vector4 } from '../src/math/Vector4.js';

describe('Vector4', () => {
  let vector;

  beforeEach(() => {
    vector = new Vector4();
  });

  describe('Constructor', () => {
    test('should create a Vector4 with default values (0, 0, 0, 1)', () => {
      const v = new Vector4();
      expect(v.x).toBe(0);
      expect(v.y).toBe(0);
      expect(v.z).toBe(0);
      expect(v.w).toBe(1);
      expect(v.isVector4).toBe(true);
    });

    test('should create a Vector4 with specified values', () => {
      const v = new Vector4(1, 2, 3, 4);
      expect(v.x).toBe(1);
      expect(v.y).toBe(2);
      expect(v.z).toBe(3);
      expect(v.w).toBe(4);
    });
  });

  describe('Properties and Getters/Setters', () => {
    test('should have width and height getters/setters', () => {
      vector.width = 5;
      vector.height = 6;
      expect(vector.z).toBe(5);
      expect(vector.w).toBe(6);
      expect(vector.width).toBe(5);
      expect(vector.height).toBe(6);
    });

    test('should set individual components', () => {
      vector.setX(1).setY(2).setZ(3).setW(4);
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(4);
    });
  });

  describe('Basic Operations', () => {
    test('set() should set all components', () => {
      vector.set(1, 2, 3, 4);
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(4);
    });

    test('setScalar() should set all components to the same value', () => {
      vector.setScalar(5);
      expect(vector.x).toBe(5);
      expect(vector.y).toBe(5);
      expect(vector.z).toBe(5);
      expect(vector.w).toBe(5);
    });

    test('setComponent() and getComponent() should work correctly', () => {
      vector.setComponent(0, 1);
      vector.setComponent(1, 2);
      vector.setComponent(2, 3);
      vector.setComponent(3, 4);
      
      expect(vector.getComponent(0)).toBe(1);
      expect(vector.getComponent(1)).toBe(2);
      expect(vector.getComponent(2)).toBe(3);
      expect(vector.getComponent(3)).toBe(4);
    });

    test('setComponent() should throw error for invalid index', () => {
      expect(() => vector.setComponent(4, 1)).toThrow('index is out of range: 4');
      expect(() => vector.setComponent(-1, 1)).toThrow('index is out of range: -1');
    });

    test('getComponent() should throw error for invalid index', () => {
      expect(() => vector.getComponent(4)).toThrow('index is out of range: 4');
      expect(() => vector.getComponent(-1)).toThrow('index is out of range: -1');
    });
  });

  describe('Clone and Copy', () => {
    test('clone() should create an identical copy', () => {
      vector.set(1, 2, 3, 4);
      const cloned = vector.clone();
      
      expect(cloned).not.toBe(vector);
      expect(cloned.x).toBe(vector.x);
      expect(cloned.y).toBe(vector.y);
      expect(cloned.z).toBe(vector.z);
      expect(cloned.w).toBe(vector.w);
      expect(cloned.isVector4).toBe(true);
    });

    test('copy() should copy values from another vector', () => {
      const source = new Vector4(5, 6, 7, 8);
      vector.copy(source);
      
      expect(vector.x).toBe(5);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(7);
      expect(vector.w).toBe(8);
    });

    test('copy() should handle Vector3-like objects (missing w)', () => {
      const source = { x: 1, y: 2, z: 3 };
      vector.copy(source);
      
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(1); // Default value when w is undefined
    });
  });

  describe('Addition Operations', () => {
    test('add() should add another vector', () => {
      vector.set(1, 2, 3, 4);
      const other = new Vector4(5, 6, 7, 8);
      vector.add(other);
      
      expect(vector.x).toBe(6);
      expect(vector.y).toBe(8);
      expect(vector.z).toBe(10);
      expect(vector.w).toBe(12);
    });

    test('addScalar() should add scalar to all components', () => {
      vector.set(1, 2, 3, 4);
      vector.addScalar(5);
      
      expect(vector.x).toBe(6);
      expect(vector.y).toBe(7);
      expect(vector.z).toBe(8);
      expect(vector.w).toBe(9);
    });

    test('addVectors() should add two vectors', () => {
      const a = new Vector4(1, 2, 3, 4);
      const b = new Vector4(5, 6, 7, 8);
      vector.addVectors(a, b);
      
      expect(vector.x).toBe(6);
      expect(vector.y).toBe(8);
      expect(vector.z).toBe(10);
      expect(vector.w).toBe(12);
    });

    test('addScaledVector() should add scaled vector', () => {
      vector.set(1, 2, 3, 4);
      const other = new Vector4(2, 3, 4, 5);
      vector.addScaledVector(other, 2);
      
      expect(vector.x).toBe(5);  // 1 + 2*2
      expect(vector.y).toBe(8);  // 2 + 3*2
      expect(vector.z).toBe(11); // 3 + 4*2
      expect(vector.w).toBe(14); // 4 + 5*2
    });
  });

  describe('Subtraction Operations', () => {
    test('sub() should subtract another vector', () => {
      vector.set(10, 8, 6, 4);
      const other = new Vector4(1, 2, 3, 4);
      vector.sub(other);
      
      expect(vector.x).toBe(9);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(0);
    });

    test('subScalar() should subtract scalar from all components', () => {
      vector.set(10, 8, 6, 4);
      vector.subScalar(2);
      
      expect(vector.x).toBe(8);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(4);
      expect(vector.w).toBe(2);
    });

    test('subVectors() should subtract two vectors', () => {
      const a = new Vector4(10, 8, 6, 4);
      const b = new Vector4(1, 2, 3, 4);
      vector.subVectors(a, b);
      
      expect(vector.x).toBe(9);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(0);
    });
  });

  describe('Multiplication Operations', () => {
    test('multiply() should multiply by another vector component-wise', () => {
      vector.set(2, 3, 4, 5);
      const other = new Vector4(2, 3, 2, 2);
      vector.multiply(other);
      
      expect(vector.x).toBe(4);
      expect(vector.y).toBe(9);
      expect(vector.z).toBe(8);
      expect(vector.w).toBe(10);
    });

    test('multiplyScalar() should multiply all components by scalar', () => {
      vector.set(1, 2, 3, 4);
      vector.multiplyScalar(3);
      
      expect(vector.x).toBe(3);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(9);
      expect(vector.w).toBe(12);
    });
  });

  describe('Division Operations', () => {
    test('divideScalar() should divide all components by scalar', () => {
      vector.set(6, 9, 12, 15);
      vector.divideScalar(3);
      
      expect(vector.x).toBe(2);
      expect(vector.y).toBe(3);
      expect(vector.z).toBe(4);
      expect(vector.w).toBe(5);
    });

    test('divideScalar() should handle division by zero gracefully', () => {
      vector.set(1, 2, 3, 4);
      vector.divideScalar(0);
      
      expect(vector.x).toBe(Infinity);
      expect(vector.y).toBe(Infinity);
      expect(vector.z).toBe(Infinity);
      expect(vector.w).toBe(Infinity);
    });
  });

  describe('Matrix Operations', () => {
    test('applyMatrix4() should transform vector by matrix', () => {
      // Create a simple identity matrix mock
      const matrix = {
        elements: [
          1, 0, 0, 0,
          0, 1, 0, 0,
          0, 0, 1, 0,
          0, 0, 0, 1
        ]
      };
      
      vector.set(1, 2, 3, 4);
      vector.applyMatrix4(matrix);
      
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(4);
    });

    test('applyMatrix4() should apply transformation matrix', () => {
      // Translation matrix: translate by (1, 2, 3)
      const matrix = {
        elements: [
          1, 0, 0, 0,
          0, 1, 0, 0,
          0, 0, 1, 0,
          1, 2, 3, 1
        ]
      };
      
      vector.set(0, 0, 0, 1);
      vector.applyMatrix4(matrix);
      
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(1);
    });
  });

  describe('Axis-Angle Operations', () => {
    test('setAxisAngleFromQuaternion() should set axis-angle from quaternion', () => {
      // Mock quaternion with normalized values
      const quaternion = { x: 0, y: 0, z: 0, w: 1 }; // Identity quaternion
      vector.setAxisAngleFromQuaternion(quaternion);

      expect(vector.w).toBe(0); // No rotation
      expect(vector.x).toBe(1); // Default axis when no rotation
      expect(vector.y).toBe(0);
      expect(vector.z).toBe(0);
    });

    test('setAxisAngleFromQuaternion() should handle non-identity quaternion', () => {
      // Quaternion representing 90-degree rotation around Z-axis
      const quaternion = { x: 0, y: 0, z: 0.7071067811865476, w: 0.7071067811865476 };
      vector.setAxisAngleFromQuaternion(quaternion);

      expect(vector.x).toBeCloseTo(0, 5);
      expect(vector.y).toBeCloseTo(0, 5);
      expect(vector.z).toBeCloseTo(1, 5);
      expect(vector.w).toBeCloseTo(Math.PI / 2, 5);
    });

    test('setAxisAngleFromRotationMatrix() should set axis-angle from identity matrix', () => {
      // Identity rotation matrix
      const matrix = {
        elements: [
          1, 0, 0, 0,
          0, 1, 0, 0,
          0, 0, 1, 0,
          0, 0, 0, 1
        ]
      };

      vector.setAxisAngleFromRotationMatrix(matrix);

      expect(vector.x).toBe(1);
      expect(vector.y).toBe(0);
      expect(vector.z).toBe(0);
      expect(vector.w).toBe(0); // No rotation
    });

    test('setAxisAngleFromRotationMatrix() should handle 180-degree rotation (singularity case)', () => {
      // 180-degree rotation around X-axis
      const matrix = {
        elements: [
          1, 0, 0, 0,
          0, -1, 0, 0,
          0, 0, -1, 0,
          0, 0, 0, 1
        ]
      };

      vector.setAxisAngleFromRotationMatrix(matrix);

      expect(vector.w).toBeCloseTo(Math.PI, 5); // 180 degrees
      expect(vector.x).toBeCloseTo(1, 5); // Rotation around X-axis
    });

    test('setAxisAngleFromRotationMatrix() should handle complex rotation matrix', () => {
      // 90-degree rotation around Z-axis
      const matrix = {
        elements: [
          0, 1, 0, 0,
          -1, 0, 0, 0,
          0, 0, 1, 0,
          0, 0, 0, 1
        ]
      };

      vector.setAxisAngleFromRotationMatrix(matrix);

      expect(vector.x).toBeCloseTo(0, 5);
      expect(vector.y).toBeCloseTo(0, 5);
      expect(vector.z).toBeCloseTo(1, 5);
      expect(vector.w).toBeCloseTo(Math.PI / 2, 5);
    });

    test('setAxisAngleFromRotationMatrix() should handle 180-degree rotation edge cases', () => {
      // 180-degree rotation around Y-axis (yy > zz case)
      const matrix = {
        elements: [
          -1, 0, 0, 0,
          0, 1, 0, 0,
          0, 0, -1, 0,
          0, 0, 0, 1
        ]
      };

      vector.setAxisAngleFromRotationMatrix(matrix);

      expect(vector.w).toBeCloseTo(Math.PI, 5); // 180 degrees
      expect(vector.y).toBeCloseTo(1, 5); // Rotation around Y-axis
    });

    test('setAxisAngleFromRotationMatrix() should handle 180-degree rotation around Z-axis', () => {
      // 180-degree rotation around Z-axis (zz > xx && zz > yy case)
      const matrix = {
        elements: [
          -1, 0, 0, 0,
          0, -1, 0, 0,
          0, 0, 1, 0,
          0, 0, 0, 1
        ]
      };

      vector.setAxisAngleFromRotationMatrix(matrix);

      expect(vector.w).toBeCloseTo(Math.PI, 5); // 180 degrees
      expect(vector.z).toBeCloseTo(1, 5); // Rotation around Z-axis
    });
  });

  describe('Min/Max Operations', () => {
    test('min() should take minimum components', () => {
      vector.set(5, 2, 8, 1);
      const other = new Vector4(3, 6, 4, 7);
      vector.min(other);

      expect(vector.x).toBe(3);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(4);
      expect(vector.w).toBe(1);
    });

    test('max() should take maximum components', () => {
      vector.set(5, 2, 8, 1);
      const other = new Vector4(3, 6, 4, 7);
      vector.max(other);

      expect(vector.x).toBe(5);
      expect(vector.y).toBe(6);
      expect(vector.z).toBe(8);
      expect(vector.w).toBe(7);
    });
  });

  describe('Clamping Operations', () => {
    test('clamp() should clamp components between min and max vectors', () => {
      vector.set(-5, 15, 0.5, 25);
      const min = new Vector4(-2, 0, 0, 0);
      const max = new Vector4(10, 10, 1, 20);
      vector.clamp(min, max);

      expect(vector.x).toBe(-2);
      expect(vector.y).toBe(10);
      expect(vector.z).toBe(0.5);
      expect(vector.w).toBe(20);
    });

    test('clampScalar() should clamp all components between scalar values', () => {
      vector.set(-5, 15, 0.5, 25);
      vector.clampScalar(0, 10);

      expect(vector.x).toBe(0);
      expect(vector.y).toBe(10);
      expect(vector.z).toBe(0.5);
      expect(vector.w).toBe(10);
    });

    test('clampLength() should clamp vector length', () => {
      vector.set(3, 4, 0, 0); // Length = 5
      vector.clampLength(2, 4);

      const length = vector.length();
      expect(length).toBeCloseTo(4, 5);
    });
  });

  describe('Rounding Operations', () => {
    test('floor() should floor all components', () => {
      vector.set(1.7, -2.3, 3.9, -4.1);
      vector.floor();

      expect(vector.x).toBe(1);
      expect(vector.y).toBe(-3);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(-5);
    });

    test('ceil() should ceil all components', () => {
      vector.set(1.2, -2.8, 3.1, -4.9);
      vector.ceil();

      expect(vector.x).toBe(2);
      expect(vector.y).toBe(-2);
      expect(vector.z).toBe(4);
      expect(vector.w).toBe(-4);
    });

    test('round() should round all components', () => {
      vector.set(1.4, 2.6, -3.4, -4.6);
      vector.round();

      expect(vector.x).toBe(1);
      expect(vector.y).toBe(3);
      expect(vector.z).toBe(-3);
      expect(vector.w).toBe(-5);
    });

    test('roundToZero() should round towards zero', () => {
      vector.set(1.7, -2.7, 3.2, -4.2);
      vector.roundToZero();

      expect(vector.x).toBe(1);
      expect(vector.y).toBe(-2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(-4);
    });
  });

  describe('Vector Operations', () => {
    test('negate() should negate all components', () => {
      vector.set(1, -2, 3, -4);
      vector.negate();

      expect(vector.x).toBe(-1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(-3);
      expect(vector.w).toBe(4);
    });

    test('dot() should calculate dot product', () => {
      vector.set(1, 2, 3, 4);
      const other = new Vector4(2, 3, 4, 5);
      const result = vector.dot(other);

      expect(result).toBe(40); // 1*2 + 2*3 + 3*4 + 4*5 = 2 + 6 + 12 + 20 = 40
    });

    test('lengthSq() should calculate squared length', () => {
      vector.set(1, 2, 3, 4);
      const result = vector.lengthSq();

      expect(result).toBe(30); // 1² + 2² + 3² + 4² = 1 + 4 + 9 + 16 = 30
    });

    test('length() should calculate vector length', () => {
      vector.set(1, 2, 3, 4);
      const result = vector.length();

      expect(result).toBeCloseTo(Math.sqrt(30), 5);
    });

    test('manhattanLength() should calculate Manhattan distance', () => {
      vector.set(-1, 2, -3, 4);
      const result = vector.manhattanLength();

      expect(result).toBe(10); // |−1| + |2| + |−3| + |4| = 1 + 2 + 3 + 4 = 10
    });

    test('normalize() should normalize vector to unit length', () => {
      vector.set(3, 4, 0, 0);
      vector.normalize();

      expect(vector.length()).toBeCloseTo(1, 5);
      expect(vector.x).toBeCloseTo(0.6, 5);
      expect(vector.y).toBeCloseTo(0.8, 5);
    });

    test('normalize() should handle zero vector', () => {
      vector.set(0, 0, 0, 0);
      vector.normalize();

      expect(vector.x).toBe(0);
      expect(vector.y).toBe(0);
      expect(vector.z).toBe(0);
      expect(vector.w).toBe(0);
    });

    test('setLength() should set vector to specific length', () => {
      vector.set(3, 4, 0, 0);
      vector.setLength(10);

      expect(vector.length()).toBeCloseTo(10, 5);
    });
  });

  describe('Interpolation', () => {
    test('lerp() should linearly interpolate between vectors', () => {
      vector.set(0, 0, 0, 0);
      const target = new Vector4(10, 20, 30, 40);
      vector.lerp(target, 0.5);

      expect(vector.x).toBe(5);
      expect(vector.y).toBe(10);
      expect(vector.z).toBe(15);
      expect(vector.w).toBe(20);
    });

    test('lerpVectors() should interpolate between two vectors', () => {
      const a = new Vector4(0, 0, 0, 0);
      const b = new Vector4(10, 20, 30, 40);
      vector.lerpVectors(a, b, 0.25);

      expect(vector.x).toBe(2.5);
      expect(vector.y).toBe(5);
      expect(vector.z).toBe(7.5);
      expect(vector.w).toBe(10);
    });
  });

  describe('Equality and Comparison', () => {
    test('equals() should return true for equal vectors', () => {
      vector.set(1, 2, 3, 4);
      const other = new Vector4(1, 2, 3, 4);

      expect(vector.equals(other)).toBe(true);
    });

    test('equals() should return false for different vectors', () => {
      vector.set(1, 2, 3, 4);
      const other = new Vector4(1, 2, 3, 5);

      expect(vector.equals(other)).toBe(false);
    });
  });

  describe('Array Operations', () => {
    test('fromArray() should set components from array', () => {
      const array = [1, 2, 3, 4, 5, 6];
      vector.fromArray(array, 1);

      expect(vector.x).toBe(2);
      expect(vector.y).toBe(3);
      expect(vector.z).toBe(4);
      expect(vector.w).toBe(5);
    });

    test('fromArray() should use default offset', () => {
      const array = [1, 2, 3, 4];
      vector.fromArray(array);

      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(4);
    });

    test('toArray() should return components as array', () => {
      vector.set(1, 2, 3, 4);
      const result = vector.toArray();

      expect(result).toEqual([1, 2, 3, 4]);
    });

    test('toArray() should fill existing array with offset', () => {
      vector.set(1, 2, 3, 4);
      const array = [0, 0, 0, 0, 0, 0];
      vector.toArray(array, 2);

      expect(array).toEqual([0, 0, 1, 2, 3, 4]);
    });
  });

  describe('Buffer Attribute Operations', () => {
    test('fromBufferAttribute() should set components from buffer attribute', () => {
      const mockAttribute = {
        getX: jest.fn().mockReturnValue(1),
        getY: jest.fn().mockReturnValue(2),
        getZ: jest.fn().mockReturnValue(3),
        getW: jest.fn().mockReturnValue(4)
      };

      vector.fromBufferAttribute(mockAttribute, 5);

      expect(mockAttribute.getX).toHaveBeenCalledWith(5);
      expect(mockAttribute.getY).toHaveBeenCalledWith(5);
      expect(mockAttribute.getZ).toHaveBeenCalledWith(5);
      expect(mockAttribute.getW).toHaveBeenCalledWith(5);
      expect(vector.x).toBe(1);
      expect(vector.y).toBe(2);
      expect(vector.z).toBe(3);
      expect(vector.w).toBe(4);
    });

    test('fromBufferAttribute() with offset should show deprecation warning', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const mockAttribute = {
        getX: jest.fn().mockReturnValue(1),
        getY: jest.fn().mockReturnValue(2),
        getZ: jest.fn().mockReturnValue(3),
        getW: jest.fn().mockReturnValue(4)
      };

      vector.fromBufferAttribute(mockAttribute, 5, 10); // Third parameter triggers warning

      expect(consoleSpy).toHaveBeenCalledWith(
        'THREE.Vector4: offset has been removed from .fromBufferAttribute().'
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Random Operations', () => {
    test('random() should set random values between 0 and 1', () => {
      vector.random();

      expect(vector.x).toBeGreaterThanOrEqual(0);
      expect(vector.x).toBeLessThan(1);
      expect(vector.y).toBeGreaterThanOrEqual(0);
      expect(vector.y).toBeLessThan(1);
      expect(vector.z).toBeGreaterThanOrEqual(0);
      expect(vector.z).toBeLessThan(1);
      expect(vector.w).toBeGreaterThanOrEqual(0);
      expect(vector.w).toBeLessThan(1);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle very large numbers', () => {
      const large = Number.MAX_SAFE_INTEGER;
      vector.set(large, large, large, large);

      expect(vector.x).toBe(large);
      expect(vector.y).toBe(large);
      expect(vector.z).toBe(large);
      expect(vector.w).toBe(large);
    });

    test('should handle very small numbers', () => {
      const small = Number.MIN_VALUE;
      vector.set(small, small, small, small);

      expect(vector.x).toBe(small);
      expect(vector.y).toBe(small);
      expect(vector.z).toBe(small);
      expect(vector.w).toBe(small);
    });

    test('should handle NaN values', () => {
      vector.set(NaN, NaN, NaN, NaN);

      expect(isNaN(vector.x)).toBe(true);
      expect(isNaN(vector.y)).toBe(true);
      expect(isNaN(vector.z)).toBe(true);
      expect(isNaN(vector.w)).toBe(true);
    });

    test('should handle Infinity values', () => {
      vector.set(Infinity, -Infinity, Infinity, -Infinity);

      expect(vector.x).toBe(Infinity);
      expect(vector.y).toBe(-Infinity);
      expect(vector.z).toBe(Infinity);
      expect(vector.w).toBe(-Infinity);
    });
  });

  describe('Method Chaining', () => {
    test('should support method chaining', () => {
      const result = vector
        .set(1, 2, 3, 4)
        .multiplyScalar(2)
        .addScalar(1)
        .normalize();

      expect(result).toBe(vector);
      expect(vector.length()).toBeCloseTo(1, 5);
    });
  });

  describe('Deprecated Method Warnings', () => {
    test('add() with two arguments should show deprecation warning', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const a = new Vector4(1, 2, 3, 4);
      const b = new Vector4(5, 6, 7, 8);

      vector.add(a, b);

      expect(consoleSpy).toHaveBeenCalledWith(
        'THREE.Vector4: .add() now only accepts one argument. Use .addVectors( a, b ) instead.'
      );

      consoleSpy.mockRestore();
    });

    test('sub() with two arguments should show deprecation warning', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const a = new Vector4(5, 6, 7, 8);
      const b = new Vector4(1, 2, 3, 4);

      vector.sub(a, b);

      expect(consoleSpy).toHaveBeenCalledWith(
        'THREE.Vector4: .sub() now only accepts one argument. Use .subVectors( a, b ) instead.'
      );

      consoleSpy.mockRestore();
    });
  });
});
